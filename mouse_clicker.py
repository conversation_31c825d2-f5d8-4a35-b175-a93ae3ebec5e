#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标连点器程序
支持自动点击、热键控制、GUI界面等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import pyautogui
import pynput
from pynput import keyboard
import sys


class MouseClicker:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # 状态变量
        self.is_clicking = False
        self.click_count = 0
        self.target_x = 0
        self.target_y = 0
        self.click_thread = None
        self.hotkey_listener = None
        
        # 创建界面
        self.create_widgets()
        
        # 设置pyautogui安全机制
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.01
        
        # 启动热键监听
        self.start_hotkey_listener()
        
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("鼠标连点器 v1.0")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # 设置窗口图标和样式
        try:
            self.root.iconbitmap(default="")
        except:
            pass
            
        # 窗口居中
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="鼠标连点器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 点击位置设置
        pos_frame = ttk.LabelFrame(main_frame, text="点击位置设置", padding="10")
        pos_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(pos_frame, text="X坐标:").grid(row=0, column=0, sticky=tk.W)
        self.x_var = tk.StringVar(value="0")
        self.x_entry = ttk.Entry(pos_frame, textvariable=self.x_var, width=10)
        self.x_entry.grid(row=0, column=1, padx=(5, 10))
        
        ttk.Label(pos_frame, text="Y坐标:").grid(row=0, column=2, sticky=tk.W)
        self.y_var = tk.StringVar(value="0")
        self.y_entry = ttk.Entry(pos_frame, textvariable=self.y_var, width=10)
        self.y_entry.grid(row=0, column=3, padx=(5, 10))
        
        ttk.Button(pos_frame, text="获取当前位置", 
                  command=self.get_current_position).grid(row=1, column=0, columnspan=4, pady=(10, 0))
        
        # 点击设置
        click_frame = ttk.LabelFrame(main_frame, text="点击设置", padding="10")
        click_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 点击间隔
        ttk.Label(click_frame, text="点击间隔(毫秒):").grid(row=0, column=0, sticky=tk.W)
        self.interval_var = tk.StringVar(value="100")
        interval_entry = ttk.Entry(click_frame, textvariable=self.interval_var, width=10)
        interval_entry.grid(row=0, column=1, padx=(5, 0), sticky=tk.W)
        
        # 点击次数设置
        ttk.Label(click_frame, text="点击次数:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        
        self.click_mode = tk.StringVar(value="infinite")
        ttk.Radiobutton(click_frame, text="无限循环", variable=self.click_mode, 
                       value="infinite").grid(row=2, column=0, sticky=tk.W)
        ttk.Radiobutton(click_frame, text="指定次数", variable=self.click_mode, 
                       value="limited").grid(row=2, column=1, sticky=tk.W)
        
        self.count_var = tk.StringVar(value="100")
        self.count_entry = ttk.Entry(click_frame, textvariable=self.count_var, width=10)
        self.count_entry.grid(row=3, column=0, columnspan=2, pady=(5, 0), sticky=tk.W)
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        self.start_button = ttk.Button(control_frame, text="开始点击", 
                                      command=self.start_clicking, style="Accent.TButton")
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止点击", 
                                     command=self.stop_clicking, state="disabled")
        self.stop_button.grid(row=0, column=1)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="10")
        status_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))
        
        self.count_display_var = tk.StringVar(value="0")
        ttk.Label(status_frame, text="已点击:").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(status_frame, textvariable=self.count_display_var).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))
        
        # 热键说明
        help_frame = ttk.LabelFrame(main_frame, text="热键说明", padding="10")
        help_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(help_frame, text="F1: 开始点击").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(help_frame, text="F2: 停止点击").grid(row=1, column=0, sticky=tk.W)
        ttk.Label(help_frame, text="ESC: 紧急停止").grid(row=2, column=0, sticky=tk.W)
        
    def get_current_position(self):
        """获取当前鼠标位置"""
        try:
            x, y = pyautogui.position()
            self.x_var.set(str(x))
            self.y_var.set(str(y))
            messagebox.showinfo("位置获取", f"已获取当前鼠标位置: ({x}, {y})")
        except Exception as e:
            messagebox.showerror("错误", f"获取鼠标位置失败: {str(e)}")
            
    def validate_inputs(self):
        """验证输入参数"""
        try:
            # 验证坐标
            x = int(self.x_var.get())
            y = int(self.y_var.get())
            if x < 0 or y < 0:
                raise ValueError("坐标不能为负数")
                
            # 验证间隔时间
            interval = int(self.interval_var.get())
            if interval < 10:
                raise ValueError("点击间隔不能小于10毫秒")
                
            # 验证点击次数
            if self.click_mode.get() == "limited":
                count = int(self.count_var.get())
                if count <= 0:
                    raise ValueError("点击次数必须大于0")
                    
            return True
        except ValueError as e:
            messagebox.showerror("输入错误", str(e))
            return False
            
    def start_clicking(self):
        """开始点击"""
        if not self.validate_inputs():
            return
            
        if self.is_clicking:
            return
            
        self.is_clicking = True
        self.click_count = 0
        self.target_x = int(self.x_var.get())
        self.target_y = int(self.y_var.get())
        
        # 更新界面状态
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.status_var.set("点击中...")
        
        # 启动点击线程
        self.click_thread = threading.Thread(target=self.clicking_loop, daemon=True)
        self.click_thread.start()
        
    def stop_clicking(self):
        """停止点击"""
        self.is_clicking = False
        
        # 更新界面状态
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.status_var.set("已停止")
        
    def clicking_loop(self):
        """点击循环"""
        try:
            interval = int(self.interval_var.get()) / 1000.0  # 转换为秒
            max_clicks = None
            
            if self.click_mode.get() == "limited":
                max_clicks = int(self.count_var.get())
                
            while self.is_clicking:
                # 检查是否达到最大点击次数
                if max_clicks and self.click_count >= max_clicks:
                    break
                    
                # 执行点击
                pyautogui.click(self.target_x, self.target_y)
                self.click_count += 1
                
                # 更新显示
                self.root.after(0, lambda: self.count_display_var.set(str(self.click_count)))
                
                # 等待间隔
                time.sleep(interval)
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("点击错误", f"点击过程中发生错误: {str(e)}"))
        finally:
            # 确保停止点击
            self.root.after(0, self.stop_clicking)

    def start_hotkey_listener(self):
        """启动热键监听"""
        def on_press(key):
            try:
                if key == keyboard.Key.f1:
                    self.root.after(0, self.start_clicking)
                elif key == keyboard.Key.f2 or key == keyboard.Key.esc:
                    self.root.after(0, self.stop_clicking)
            except AttributeError:
                pass

        self.hotkey_listener = keyboard.Listener(on_press=on_press)
        self.hotkey_listener.daemon = True
        self.hotkey_listener.start()

    def on_closing(self):
        """窗口关闭事件处理"""
        self.stop_clicking()
        if self.hotkey_listener:
            self.hotkey_listener.stop()
        self.root.destroy()

    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = MouseClicker()
        app.run()
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")
        messagebox.showerror("程序错误", f"程序运行出错: {str(e)}")


if __name__ == "__main__":
    main()
