# 鼠标连点器

一个功能完整的Python鼠标自动点击程序，支持GUI界面、热键控制和多种点击模式。

## 功能特性

- ✅ 自动鼠标左键连续点击
- ✅ 可设置点击间隔时间（最小10毫秒）
- ✅ 支持指定点击次数或无限循环
- ✅ 直观的GUI界面操作
- ✅ 热键快速控制（F1开始，F2/ESC停止）
- ✅ 实时显示点击状态和计数
- ✅ 支持获取当前鼠标位置
- ✅ 多线程设计，界面不卡顿
- ✅ 完善的异常处理和安全机制

## 安装依赖

### 方法一：使用pip安装
```bash
pip install pyautogui pynput
```

### 方法二：使用requirements.txt
```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python mouse_clicker.py
```

## 使用说明

### 界面操作

1. **设置点击位置**：
   - 手动输入X、Y坐标
   - 或点击"获取当前位置"按钮自动获取鼠标当前位置

2. **配置点击参数**：
   - 设置点击间隔（毫秒，最小值10）
   - 选择点击模式：无限循环或指定次数

3. **开始/停止点击**：
   - 点击"开始点击"按钮或按F1键开始
   - 点击"停止点击"按钮或按F2/ESC键停止

### 热键控制

- **F1**：开始点击
- **F2**：停止点击  
- **ESC**：紧急停止

### 状态显示

程序界面会实时显示：
- 当前运行状态
- 已完成的点击次数

## 安全机制

1. **最小间隔限制**：点击间隔不能小于10毫秒，防止系统过载
2. **紧急停止**：ESC键可随时紧急停止程序
3. **鼠标安全**：移动鼠标到屏幕左上角可触发pyautogui的安全停止机制
4. **异常处理**：完善的错误处理确保程序稳定运行

## 注意事项

1. **权限要求**：
   - Windows：可能需要以管理员身份运行
   - macOS：需要在"系统偏好设置 > 安全性与隐私 > 辅助功能"中授权
   - Linux：通常无需特殊权限

2. **使用建议**：
   - 建议点击间隔不要设置过小，避免对系统造成负担
   - 使用前请确保目标位置正确，避免误操作
   - 长时间使用时注意适当休息

3. **兼容性**：
   - 支持Windows、macOS、Linux系统
   - 需要Python 3.6+版本

## 故障排除

### 常见问题

1. **程序无法启动**：
   - 检查Python版本是否为3.6+
   - 确认已安装所有依赖包

2. **点击无效果**：
   - 检查坐标设置是否正确
   - 确认目标程序窗口是否在前台

3. **热键不响应**：
   - 检查是否有其他程序占用了相同热键
   - 尝试重启程序

4. **权限问题**：
   - Windows：尝试以管理员身份运行
   - macOS：在系统设置中授予辅助功能权限

## 技术实现

- **GUI框架**：tkinter
- **鼠标控制**：pyautogui
- **热键监听**：pynput
- **多线程**：threading
- **异常处理**：完整的try-catch机制

## 开发者信息

本程序采用Python开发，遵循良好的编程规范，代码结构清晰，易于维护和扩展。

## 免责声明

本程序仅供学习和合法用途使用，请勿用于任何违法违规的自动化操作。使用者需自行承担使用风险和责任。
