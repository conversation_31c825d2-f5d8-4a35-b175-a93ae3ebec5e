#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标连点器测试脚本
用于测试程序的基本功能
"""

import unittest
import sys
import os
import time
from unittest.mock import Mock, patch, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestMouseClicker(unittest.TestCase):
    """鼠标连点器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # Mock外部依赖
        self.mock_pyautogui = Mock()
        self.mock_pynput = Mock()
        self.mock_keyboard = Mock()
        
        # 设置mock模块
        sys.modules['pyautogui'] = self.mock_pyautogui
        sys.modules['pynput'] = self.mock_pynput
        sys.modules['pynput.keyboard'] = self.mock_keyboard
        
        # 设置pyautogui的属性
        self.mock_pyautogui.FAILSAFE = True
        self.mock_pyautogui.PAUSE = 0.01
        self.mock_pyautogui.position.return_value = (100, 200)
        
        # 设置keyboard的属性
        self.mock_keyboard.Key = Mock()
        self.mock_keyboard.Key.f1 = 'f1'
        self.mock_keyboard.Key.f2 = 'f2'
        self.mock_keyboard.Key.esc = 'esc'
        self.mock_keyboard.Listener = Mock()
        
    def tearDown(self):
        """测试后清理"""
        # 清理mock模块
        modules_to_remove = ['pyautogui', 'pynput', 'pynput.keyboard', 'mouse_clicker']
        for module in modules_to_remove:
            if module in sys.modules:
                del sys.modules[module]
    
    @patch('tkinter.Tk')
    def test_mouse_clicker_init(self, mock_tk):
        """测试MouseClicker初始化"""
        try:
            from mouse_clicker import MouseClicker
            
            # 创建实例
            clicker = MouseClicker()
            
            # 验证初始状态
            self.assertFalse(clicker.is_clicking)
            self.assertEqual(clicker.click_count, 0)
            self.assertEqual(clicker.target_x, 0)
            self.assertEqual(clicker.target_y, 0)
            
            print("✅ MouseClicker初始化测试通过")
            
        except Exception as e:
            print(f"❌ MouseClicker初始化测试失败: {e}")
            
    def test_input_validation(self):
        """测试输入验证功能"""
        try:
            from mouse_clicker import MouseClicker
            
            with patch('tkinter.Tk'):
                clicker = MouseClicker()
                
                # 测试有效输入
                clicker.x_var.set("100")
                clicker.y_var.set("200")
                clicker.interval_var.set("50")
                clicker.click_mode.set("limited")
                clicker.count_var.set("10")
                
                # 这里需要mock messagebox来避免GUI弹窗
                with patch('tkinter.messagebox.showerror'):
                    result = clicker.validate_inputs()
                    self.assertTrue(result)
                
                print("✅ 输入验证测试通过")
                
        except Exception as e:
            print(f"❌ 输入验证测试失败: {e}")
    
    def test_position_getting(self):
        """测试获取鼠标位置功能"""
        try:
            from mouse_clicker import MouseClicker
            
            with patch('tkinter.Tk'):
                clicker = MouseClicker()
                
                # Mock messagebox
                with patch('tkinter.messagebox.showinfo') as mock_info:
                    clicker.get_current_position()
                    
                    # 验证pyautogui.position被调用
                    self.mock_pyautogui.position.assert_called_once()
                    
                    # 验证坐标被设置
                    self.assertEqual(clicker.x_var.get(), "100")
                    self.assertEqual(clicker.y_var.get(), "200")
                    
                    # 验证信息框被显示
                    mock_info.assert_called_once()
                
                print("✅ 获取鼠标位置测试通过")
                
        except Exception as e:
            print(f"❌ 获取鼠标位置测试失败: {e}")

def run_basic_tests():
    """运行基本功能测试"""
    print("开始运行鼠标连点器基本功能测试...")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查依赖包
    print("\n检查依赖包:")
    dependencies = ['tkinter', 'threading', 'time']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: 已安装")
        except ImportError:
            print(f"❌ {dep}: 未安装")
    
    # 检查外部依赖（可能未安装）
    external_deps = ['pyautogui', 'pynput']
    print("\n检查外部依赖:")
    
    for dep in external_deps:
        try:
            __import__(dep)
            print(f"✅ {dep}: 已安装")
        except ImportError:
            print(f"⚠️  {dep}: 未安装（运行程序前需要安装）")
    
    # 运行单元测试
    print("\n运行单元测试:")
    print("-" * 30)
    
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMouseClicker)
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(suite)
    
    if result.wasSuccessful():
        print("✅ 所有单元测试通过")
    else:
        print(f"❌ {len(result.failures)} 个测试失败, {len(result.errors)} 个错误")
    
    print("\n测试完成!")
    print("=" * 50)

def main():
    """主函数"""
    print("鼠标连点器测试工具")
    print("=" * 50)
    
    choice = input("选择测试模式:\n1. 基本功能测试\n2. 单元测试\n3. 全部测试\n请输入选择 (1-3): ").strip()
    
    if choice == '1':
        run_basic_tests()
    elif choice == '2':
        unittest.main(verbosity=2, exit=False)
    elif choice == '3':
        run_basic_tests()
        print("\n" + "=" * 50)
        unittest.main(verbosity=2, exit=False)
    else:
        print("无效选择，运行基本功能测试...")
        run_basic_tests()

if __name__ == "__main__":
    main()
