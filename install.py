#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标连点器安装脚本
自动安装所需依赖并进行环境检查
"""

import sys
import subprocess
import platform
import os

def print_header():
    """打印标题"""
    print("=" * 60)
    print("           鼠标连点器 - 依赖安装脚本")
    print("=" * 60)
    print()

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    print(f"   当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        print("   请升级Python后重试")
        return False
    else:
        print("✅ Python版本检查通过")
        return True

def check_pip():
    """检查pip是否可用"""
    print("\n🔍 检查pip...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', '--version'], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip不可用")
        print("   请先安装pip")
        return False

def upgrade_pip():
    """升级pip"""
    print("\n🔄 升级pip到最新版本...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
        print("✅ pip升级完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  pip升级失败: {e}")
        print("   继续安装依赖...")
        return False

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    dependencies = [
        'pyautogui>=0.9.54',
        'pynput>=1.7.6'
    ]
    
    success_count = 0
    
    for dep in dependencies:
        print(f"   正在安装 {dep}...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"   ✅ {dep} 安装成功")
            success_count += 1
        except subprocess.CalledProcessError as e:
            print(f"   ❌ {dep} 安装失败: {e}")
    
    return success_count == len(dependencies)

def check_system_requirements():
    """检查系统要求"""
    print("\n🖥️  检查系统环境...")
    system = platform.system()
    print(f"   操作系统: {system} {platform.release()}")
    
    if system == "Darwin":  # macOS
        print("   ℹ️  macOS用户注意:")
        print("      - 可能需要在'系统偏好设置 > 安全性与隐私 > 辅助功能'中授权")
        print("      - 首次运行时系统可能会弹出权限请求")
    elif system == "Windows":
        print("   ℹ️  Windows用户注意:")
        print("      - 某些情况下可能需要以管理员身份运行")
        print("      - 如果遇到权限问题，请右键选择'以管理员身份运行'")
    elif system == "Linux":
        print("   ℹ️  Linux用户注意:")
        print("      - 通常无需特殊权限")
        print("      - 确保X11或Wayland显示服务器正常运行")

def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    packages = ['pyautogui', 'pynput']
    all_success = True
    
    for package in packages:
        try:
            __import__(package)
            print(f"   ✅ {package} 导入成功")
        except ImportError as e:
            print(f"   ❌ {package} 导入失败: {e}")
            all_success = False
    
    return all_success

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if platform.system() != "Windows":
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "鼠标连点器.lnk")
        target = os.path.join(os.getcwd(), "run.py")
        wDir = os.getcwd()
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        print("   ✅ 桌面快捷方式创建成功")
    except ImportError:
        print("   ℹ️  跳过桌面快捷方式创建（需要pywin32）")
    except Exception as e:
        print(f"   ⚠️  桌面快捷方式创建失败: {e}")

def main():
    """主函数"""
    print_header()
    
    # 检查Python版本
    if not check_python_version():
        input("\n按回车键退出...")
        return
    
    # 检查pip
    if not check_pip():
        input("\n按回车键退出...")
        return
    
    # 升级pip
    upgrade_pip()
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 部分依赖安装失败")
        print("   请检查网络连接或手动安装:")
        print("   pip install pyautogui pynput")
        input("\n按回车键退出...")
        return
    
    # 验证安装
    if not verify_installation():
        print("\n❌ 安装验证失败")
        input("\n按回车键退出...")
        return
    
    # 检查系统要求
    check_system_requirements()
    
    # 创建桌面快捷方式（仅Windows）
    if platform.system() == "Windows":
        create_desktop_shortcut()
    
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("\n📋 使用说明:")
    print("   1. 运行程序: python mouse_clicker.py")
    print("   2. 或使用启动脚本: python run.py")
    print("   3. Windows用户可双击: 启动鼠标连点器.bat")
    print("\n📖 详细说明请查看 README.md 文件")
    print("\n🧪 运行测试: python test_clicker.py")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
