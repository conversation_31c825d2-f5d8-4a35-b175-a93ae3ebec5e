#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鼠标连点器启动脚本
检查依赖并启动主程序
"""

import sys
import subprocess
import importlib.util

def check_and_install_dependencies():
    """检查并安装依赖"""
    required_packages = {
        'pyautogui': 'pyautogui>=0.9.54',
        'pynput': 'pynput>=1.7.6'
    }
    
    missing_packages = []
    
    for package_name, package_spec in required_packages.items():
        if importlib.util.find_spec(package_name) is None:
            missing_packages.append(package_spec)
    
    if missing_packages:
        print("检测到缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        response = input("\n是否自动安装缺少的依赖包? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            try:
                for package in missing_packages:
                    print(f"正在安装 {package}...")
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print("依赖安装完成!")
            except subprocess.CalledProcessError as e:
                print(f"安装依赖时出错: {e}")
                print("请手动运行: pip install -r requirements.txt")
                return False
        else:
            print("请手动安装依赖后再运行程序:")
            print("pip install -r requirements.txt")
            return False
    
    return True

def main():
    """主函数"""
    print("鼠标连点器启动中...")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        print(f"当前版本: {sys.version}")
        return
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        return
    
    # 启动主程序
    try:
        print("启动鼠标连点器...")
        from mouse_clicker import main as run_clicker
        run_clicker()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保mouse_clicker.py文件存在")
    except Exception as e:
        print(f"程序运行错误: {e}")

if __name__ == "__main__":
    main()
