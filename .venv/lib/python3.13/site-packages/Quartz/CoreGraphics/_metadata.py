# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:06:07 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "CGFunctionCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGFunctionCallbacks",
            b"{CGFunctionCallbacks=I^?^?}",
            [],
        ),
        "CGDeviceByteColor": objc.createStructType(
            "Quartz.CoreGraphics.CGDeviceByteColor",
            b"{CGDeviceByteColor=CCC}",
            ["red", "green", "blue"],
        ),
        "CGDataProviderCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGDataProviderCallbacks",
            b"{CGDataProviderCallbacks=^?^?^?^?}",
            [],
        ),
        "CGColorBufferFormat": objc.createStructType(
            "Quartz.CoreGraphics.CGColorBufferFormat",
            b"{CGColorBufferFormat=IIQQQ}",
            [
                "version",
                "bitmapInfo",
                "bitsPerComponent",
                "bitsPerPixel",
                "bytesPerRow",
            ],
        ),
        "CGPatternCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGPatternCallbacks", b"{CGPatternCallbacks=I^?^?}", []
        ),
        "CGScreenUpdateMoveDelta": objc.createStructType(
            "Quartz.CoreGraphics.CGScreenUpdateMoveDelta",
            b"{CGScreenUpdateMoveDelta=ii}",
            ["dX", "dY"],
        ),
        "CGDeviceColor": objc.createStructType(
            "Quartz.CoreGraphics.CGDeviceColor",
            b"{CGDeviceColor=fff}",
            ["red", "green", "blue"],
        ),
        "CGColorDataFormat": objc.createStructType(
            "Quartz.CoreGraphics.CGColorDataFormat",
            b"{CGColorDataFormat=I@IQQi^d}",
            [
                "version",
                "colorspace_info",
                "bitmap_info",
                "bits_per_component",
                "bytes_per_row",
                "intent",
                "decode",
            ],
        ),
        "CGDataConsumerCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGDataConsumerCallbacks",
            b"{CGDataConsumerCallbacks=^?^?}",
            [],
        ),
        "CGDataProviderDirectAccessCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGDataProviderDirectAccessCallbacks",
            b"{CGDataProviderDirectAccessCallbacks=^?^?^?^?}",
            [],
        ),
        "CGPathElement": objc.createStructType(
            "Quartz.CoreGraphics.CGPathElement",
            b"{CGPathElement=i^{CGPoint=dd}}",
            ["type", "points"],
        ),
        "CGDataProviderSequentialCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGDataProviderSequentialCallbacks",
            b"{CGDataProviderSequentialCallbacks=I^?^?^?^?}",
            [],
        ),
        "CGEventTapInformation": objc.createStructType(
            "Quartz.CoreGraphics.CGEventTapInformation",
            b"{__CGEventTapInformation=IIIQiiBfff}",
            [
                "eventTapID",
                "tapPoint",
                "options",
                "eventsOfInterest",
                "tappingProcess",
                "processBeingTapped",
                "enabled",
                "minUsecLatency",
                "avgUsecLatency",
                "maxUsecLatency",
            ],
        ),
        "CGPSConverterCallbacks": objc.createStructType(
            "Quartz.CoreGraphics.CGPSConverterCallbacks",
            b"{CGPSConverterCallbacks=I^?^?^?^?^?^?^?}",
            [],
        ),
    }
)
constants = """$CGAffineTransformIdentity@{CGAffineTransform=dddddd}$CGPointZero@{CGPoint=dd}$CGRectInfinite@{CGRect={CGPoint=dd}{CGSize=dd}}$CGRectNull@{CGRect={CGPoint=dd}{CGSize=dd}}$CGRectZero@{CGRect={CGPoint=dd}{CGSize=dd}}$CGSizeZero@{CGSize=dd}$kCGColorBlack$kCGColorClear$kCGColorConversionBlackPointCompensation$kCGColorConversionTRCSize$kCGColorSpaceACESCGLinear$kCGColorSpaceAdobeRGB1998$kCGColorSpaceCoreMedia709$kCGColorSpaceDCIP3$kCGColorSpaceDisplayP3$kCGColorSpaceDisplayP3_HLG$kCGColorSpaceDisplayP3_PQ$kCGColorSpaceDisplayP3_PQ_EOTF$kCGColorSpaceExtendedDisplayP3$kCGColorSpaceExtendedGray$kCGColorSpaceExtendedITUR_2020$kCGColorSpaceExtendedLinearDisplayP3$kCGColorSpaceExtendedLinearGray$kCGColorSpaceExtendedLinearITUR_2020$kCGColorSpaceExtendedLinearSRGB$kCGColorSpaceExtendedRange$kCGColorSpaceExtendedSRGB$kCGColorSpaceGenericCMYK$kCGColorSpaceGenericGray$kCGColorSpaceGenericGrayGamma2_2$kCGColorSpaceGenericLab$kCGColorSpaceGenericRGB$kCGColorSpaceGenericRGBLinear$kCGColorSpaceGenericXYZ$kCGColorSpaceITUR_2020$kCGColorSpaceITUR_2020_HLG$kCGColorSpaceITUR_2020_PQ$kCGColorSpaceITUR_2020_PQ_EOTF$kCGColorSpaceITUR_2020_sRGBGamma$kCGColorSpaceITUR_2100_HLG$kCGColorSpaceITUR_2100_PQ$kCGColorSpaceITUR_709$kCGColorSpaceITUR_709_HLG$kCGColorSpaceITUR_709_PQ$kCGColorSpaceLinearDisplayP3$kCGColorSpaceLinearGray$kCGColorSpaceLinearITUR_2020$kCGColorSpaceLinearSRGB$kCGColorSpaceROMMRGB$kCGColorSpaceSRGB$kCGColorWhite$kCGDefaultHDRImageContentHeadroom@f$kCGDisplayShowDuplicateLowResolutionModes$kCGDisplayStreamColorSpace$kCGDisplayStreamDestinationRect$kCGDisplayStreamMinimumFrameTime$kCGDisplayStreamPreserveAspectRatio$kCGDisplayStreamQueueDepth$kCGDisplayStreamShowCursor$kCGDisplayStreamSourceRect$kCGDisplayStreamYCbCrMatrix$kCGDisplayStreamYCbCrMatrix_ITU_R_601_4$kCGDisplayStreamYCbCrMatrix_ITU_R_709_2$kCGDisplayStreamYCbCrMatrix_SMPTE_240M_1995$kCGEXRToneMappingGammaDefog$kCGEXRToneMappingGammaExposure$kCGEXRToneMappingGammaKneeHigh$kCGEXRToneMappingGammaKneeLow$kCGFontVariationAxisDefaultValue$kCGFontVariationAxisMaxValue$kCGFontVariationAxisMinValue$kCGFontVariationAxisName$kCGImageDestinationPreserveGainMap$kCGPDFContextAccessPermissions$kCGPDFContextAllowsCopying$kCGPDFContextAllowsPrinting$kCGPDFContextArtBox$kCGPDFContextAuthor$kCGPDFContextBleedBox$kCGPDFContextCreateLinearizedPDF$kCGPDFContextCreatePDFA$kCGPDFContextCreator$kCGPDFContextCropBox$kCGPDFContextEncryptionKeyLength$kCGPDFContextKeywords$kCGPDFContextMediaBox$kCGPDFContextOutputIntent$kCGPDFContextOutputIntents$kCGPDFContextOwnerPassword$kCGPDFContextSubject$kCGPDFContextTitle$kCGPDFContextTrimBox$kCGPDFContextUserPassword$kCGPDFOutlineChildren$kCGPDFOutlineDestination$kCGPDFOutlineDestinationRect$kCGPDFOutlineTitle$kCGPDFTagPropertyActualText$kCGPDFTagPropertyAlternativeText$kCGPDFTagPropertyLanguageText$kCGPDFTagPropertyTitleText$kCGPDFXDestinationOutputProfile$kCGPDFXInfo$kCGPDFXOutputCondition$kCGPDFXOutputConditionIdentifier$kCGPDFXOutputIntentSubtype$kCGPDFXRegistryName$kCGSkipBoostToHDR$kCGUse100nitsHLGOOTF$kCGUseBT1886ForCoreVideoGamma$kCGUseLegacyHDREcosystem$kCGWindowAlpha$kCGWindowBackingLocationVideoMemory$kCGWindowBounds$kCGWindowIsOnscreen$kCGWindowLayer$kCGWindowMemoryUsage$kCGWindowName$kCGWindowNumber$kCGWindowOwnerName$kCGWindowOwnerPID$kCGWindowSharingState$kCGWindowStoreType$kCGWindowWorkspace$kCIImageAuxiliarySemanticSegmentationGlassesMatte$kCIImageAuxiliarySemanticSegmentationSkyMatte$kCIImageRepresentationSemanticSegmentationGlassesMatteImage$kCIImageRepresentationSemanticSegmentationSkyMatteImage$kCIImageToneMapHDRtoSDR$kCIInputLocalToneMapAmountKey$"""
enums = """$CGFLOAT_DEFINED@1$CGFLOAT_IS_DOUBLE@1$CGGlyphMax@65534$CGGlyphMin@0$CGPDFDataFormatJPEG2000@2$CGPDFDataFormatJPEGEncoded@1$CGPDFDataFormatRaw@0$CGPDFTagTypeAnnotation@507$CGPDFTagTypeArt@102$CGPDFTagTypeBibliography@504$CGPDFTagTypeBlockQuote@105$CGPDFTagTypeCaption@106$CGPDFTagTypeCode@505$CGPDFTagTypeDiv@104$CGPDFTagTypeDocument@100$CGPDFTagTypeFigure@700$CGPDFTagTypeForm@702$CGPDFTagTypeFormula@701$CGPDFTagTypeHeader@201$CGPDFTagTypeHeader1@202$CGPDFTagTypeHeader2@203$CGPDFTagTypeHeader3@204$CGPDFTagTypeHeader4@205$CGPDFTagTypeHeader5@206$CGPDFTagTypeHeader6@207$CGPDFTagTypeIndex@109$CGPDFTagTypeLabel@302$CGPDFTagTypeLink@506$CGPDFTagTypeList@300$CGPDFTagTypeListBody@303$CGPDFTagTypeListItem@301$CGPDFTagTypeNonStructure@110$CGPDFTagTypeNote@502$CGPDFTagTypeObject@800$CGPDFTagTypeParagraph@200$CGPDFTagTypePart@101$CGPDFTagTypePrivate@111$CGPDFTagTypeQuote@501$CGPDFTagTypeReference@503$CGPDFTagTypeRuby@600$CGPDFTagTypeRubyAnnotationText@602$CGPDFTagTypeRubyBaseText@601$CGPDFTagTypeRubyPunctuation@603$CGPDFTagTypeSection@103$CGPDFTagTypeSpan@500$CGPDFTagTypeTOC@107$CGPDFTagTypeTOCI@108$CGPDFTagTypeTable@400$CGPDFTagTypeTableBody@405$CGPDFTagTypeTableDataCell@403$CGPDFTagTypeTableFooter@406$CGPDFTagTypeTableHeader@404$CGPDFTagTypeTableHeaderCell@402$CGPDFTagTypeTableRow@401$CGPDFTagTypeWarichu@604$CGPDFTagTypeWarichuPunctiation@606$CGPDFTagTypeWarichuText@605$CGRectMaxXEdge@2$CGRectMaxYEdge@3$CGRectMinXEdge@0$CGRectMinYEdge@1$CGVECTOR_DEFINED@1$CG_HDR_BT_2100@1$kCGAnnotatedSessionEventTap@2$kCGAssistiveTechHighWindowLevel@1500$kCGAssistiveTechHighWindowLevelKey@20$kCGBackingStoreBuffered@2$kCGBackingStoreNonretained@1$kCGBackingStoreRetained@0$kCGBackstopMenuLevel@-20$kCGBackstopMenuLevelKey@3$kCGBaseWindowLevelKey@0$kCGBitmapAlphaInfoMask@31$kCGBitmapByteOrder16Big@12288$kCGBitmapByteOrder16Little@4096$kCGBitmapByteOrder32Big@16384$kCGBitmapByteOrder32Little@8192$kCGBitmapByteOrderDefault@0$kCGBitmapByteOrderMask@28672$kCGBitmapFloatComponents@256$kCGBitmapFloatInfoMask@3840$kCGBlendModeClear@16$kCGBlendModeColor@14$kCGBlendModeColorBurn@7$kCGBlendModeColorDodge@6$kCGBlendModeCopy@17$kCGBlendModeDarken@4$kCGBlendModeDestinationAtop@24$kCGBlendModeDestinationIn@22$kCGBlendModeDestinationOut@23$kCGBlendModeDestinationOver@21$kCGBlendModeDifference@10$kCGBlendModeExclusion@11$kCGBlendModeHardLight@9$kCGBlendModeHue@12$kCGBlendModeLighten@5$kCGBlendModeLuminosity@15$kCGBlendModeMultiply@1$kCGBlendModeNormal@0$kCGBlendModeOverlay@3$kCGBlendModePlusDarker@26$kCGBlendModePlusLighter@27$kCGBlendModeSaturation@13$kCGBlendModeScreen@2$kCGBlendModeSoftLight@8$kCGBlendModeSourceAtop@20$kCGBlendModeSourceIn@18$kCGBlendModeSourceOut@19$kCGBlendModeXOR@25$kCGCaptureNoFill@1$kCGCaptureNoOptions@0$kCGColorConversionTransformApplySpace@2$kCGColorConversionTransformFromSpace@0$kCGColorConversionTransformToSpace@1$kCGColorConverterTransformApplySpace@2$kCGColorConverterTransformFromSpace@0$kCGColorConverterTransformToSpace@1$kCGColorSpaceModelCMYK@2$kCGColorSpaceModelDeviceN@4$kCGColorSpaceModelIndexed@5$kCGColorSpaceModelLab@3$kCGColorSpaceModelMonochrome@0$kCGColorSpaceModelPattern@6$kCGColorSpaceModelRGB@1$kCGColorSpaceModelUnknown@-1$kCGColorSpaceModelXYZ@7$kCGConfigureForAppOnly@0$kCGConfigureForSession@1$kCGConfigurePermanently@2$kCGCursorWindowLevelKey@19$kCGDesktopIconWindowLevelKey@18$kCGDesktopWindowLevelKey@2$kCGDisplayAddFlag@16$kCGDisplayBeginConfigurationFlag@1$kCGDisplayDesktopShapeChangedFlag@4096$kCGDisplayDisabledFlag@512$kCGDisplayEnabledFlag@256$kCGDisplayFadeReservationInvalidToken@0$kCGDisplayMirrorFlag@1024$kCGDisplayMovedFlag@2$kCGDisplayRemoveFlag@32$kCGDisplaySetMainFlag@4$kCGDisplaySetModeFlag@8$kCGDisplayStreamFrameStatusFrameBlank@2$kCGDisplayStreamFrameStatusFrameComplete@0$kCGDisplayStreamFrameStatusFrameIdle@1$kCGDisplayStreamFrameStatusStopped@3$kCGDisplayStreamUpdateDirtyRects@2$kCGDisplayStreamUpdateMovedRects@1$kCGDisplayStreamUpdateReducedDirtyRects@3$kCGDisplayStreamUpdateRefreshedRects@0$kCGDisplayUnMirrorFlag@2048$kCGDockWindowLevel@20$kCGDockWindowLevelKey@7$kCGDraggingWindowLevel@500$kCGDraggingWindowLevelKey@12$kCGEncodingFontSpecific@0$kCGEncodingMacRoman@1$kCGErrorApplicationAlreadyRunning@1025$kCGErrorApplicationCanOnlyBeRunInOneSessionAtATime@1026$kCGErrorApplicationIncorrectExecutableFormatFound@1023$kCGErrorApplicationIsLaunching@1024$kCGErrorApplicationNotPermittedToExecute@1016$kCGErrorApplicationRequiresNewerSystem@1015$kCGErrorCannotComplete@1004$kCGErrorClassicApplicationsMustBeLaunchedByClassic@1027$kCGErrorFailure@1000$kCGErrorFirst@1000$kCGErrorForkFailed@1028$kCGErrorIllegalArgument@1001$kCGErrorInvalidConnection@1002$kCGErrorInvalidContext@1003$kCGErrorInvalidOperation@1010$kCGErrorLast@1029$kCGErrorNameTooLong@1005$kCGErrorNoCurrentPoint@1009$kCGErrorNoneAvailable@1011$kCGErrorNotImplemented@1006$kCGErrorRangeCheck@1007$kCGErrorRetryRegistration@1029$kCGErrorSuccess@0$kCGErrorTypeCheck@1008$kCGEventFilterMaskPermitLocalKeyboardEvents@2$kCGEventFilterMaskPermitLocalMouseEvents@1$kCGEventFilterMaskPermitSystemDefinedEvents@4$kCGEventFlagMaskAlphaShift@65536$kCGEventFlagMaskAlternate@524288$kCGEventFlagMaskCommand@1048576$kCGEventFlagMaskControl@262144$kCGEventFlagMaskHelp@4194304$kCGEventFlagMaskNonCoalesced@256$kCGEventFlagMaskNumericPad@2097152$kCGEventFlagMaskSecondaryFn@8388608$kCGEventFlagMaskShift@131072$kCGEventFlagsChanged@12$kCGEventKeyDown@10$kCGEventKeyUp@11$kCGEventLeftMouseDown@1$kCGEventLeftMouseDragged@6$kCGEventLeftMouseUp@2$kCGEventMaskForAllEvents@18446744073709551615$kCGEventMouseMoved@5$kCGEventMouseSubtypeDefault@0$kCGEventMouseSubtypeTabletPoint@1$kCGEventMouseSubtypeTabletProximity@2$kCGEventNull@0$kCGEventOtherMouseDown@25$kCGEventOtherMouseDragged@27$kCGEventOtherMouseUp@26$kCGEventRightMouseDown@3$kCGEventRightMouseDragged@7$kCGEventRightMouseUp@4$kCGEventScrollWheel@22$kCGEventSourceGroupID@44$kCGEventSourceStateCombinedSessionState@0$kCGEventSourceStateHIDSystemState@1$kCGEventSourceStateID@45$kCGEventSourceStatePrivate@-1$kCGEventSourceUnixProcessID@41$kCGEventSourceUserData@42$kCGEventSourceUserID@43$kCGEventSuppressionStateRemoteMouseDrag@1$kCGEventSuppressionStateSuppressionInterval@0$kCGEventTabletPointer@23$kCGEventTabletProximity@24$kCGEventTapDisabledByTimeout@4294967294$kCGEventTapDisabledByUserInput@4294967295$kCGEventTapOptionDefault@0$kCGEventTapOptionListenOnly@1$kCGEventTargetProcessSerialNumber@39$kCGEventTargetUnixProcessID@40$kCGEventUnacceleratedPointerMovementX@170$kCGEventUnacceleratedPointerMovementY@171$kCGFloatingWindowLevel@3$kCGFloatingWindowLevelKey@5$kCGFontIndexInvalid@65535$kCGFontIndexMax@65534$kCGFontPostScriptFormatType1@1$kCGFontPostScriptFormatType3@3$kCGFontPostScriptFormatType42@42$kCGGesturePhaseBegan@1$kCGGesturePhaseCancelled@8$kCGGesturePhaseChanged@2$kCGGesturePhaseEnded@4$kCGGesturePhaseMayBegin@128$kCGGesturePhaseNone@0$kCGGlyphMax@65534$kCGGradientDrawsAfterEndLocation@2$kCGGradientDrawsBeforeStartLocation@1$kCGHIDEventTap@0$kCGHeadInsertEventTap@0$kCGHelpWindowLevel@200$kCGHelpWindowLevelKey@16$kCGImageAlphaFirst@4$kCGImageAlphaLast@3$kCGImageAlphaNone@0$kCGImageAlphaNoneSkipFirst@6$kCGImageAlphaNoneSkipLast@5$kCGImageAlphaOnly@7$kCGImageAlphaPremultipliedFirst@2$kCGImageAlphaPremultipliedLast@1$kCGImageByteOrder16Big@12288$kCGImageByteOrder16Little@4096$kCGImageByteOrder32Big@16384$kCGImageByteOrder32Little@8192$kCGImageByteOrderDefault@0$kCGImageByteOrderMask@28672$kCGImagePixelFormatMask@983040$kCGImagePixelFormatPacked@0$kCGImagePixelFormatRGB101010@196608$kCGImagePixelFormatRGB555@65536$kCGImagePixelFormatRGB565@131072$kCGImagePixelFormatRGBCIF10@262144$kCGInterpolationDefault@0$kCGInterpolationHigh@3$kCGInterpolationLow@2$kCGInterpolationMedium@4$kCGInterpolationNone@1$kCGKeyboardEventAutorepeat@8$kCGKeyboardEventKeyboardType@10$kCGKeyboardEventKeycode@9$kCGLineCapButt@0$kCGLineCapRound@1$kCGLineCapSquare@2$kCGLineJoinBevel@2$kCGLineJoinMiter@0$kCGLineJoinRound@1$kCGMainMenuWindowLevel@24$kCGMainMenuWindowLevelKey@8$kCGMaximumWindowLevelKey@14$kCGMinimumWindowLevelKey@1$kCGModalPanelWindowLevel@8$kCGModalPanelWindowLevelKey@10$kCGMomentumScrollPhaseBegin@1$kCGMomentumScrollPhaseContinue@2$kCGMomentumScrollPhaseEnd@3$kCGMomentumScrollPhaseNone@0$kCGMouseButtonCenter@2$kCGMouseButtonLeft@0$kCGMouseButtonRight@1$kCGMouseEventButtonNumber@3$kCGMouseEventClickState@1$kCGMouseEventDeltaX@4$kCGMouseEventDeltaY@5$kCGMouseEventInstantMouser@6$kCGMouseEventNumber@0$kCGMouseEventPressure@2$kCGMouseEventSubtype@7$kCGMouseEventWindowUnderMousePointer@91$kCGMouseEventWindowUnderMousePointerThatCanHandleThisEvent@92$kCGNormalWindowLevel@0$kCGNormalWindowLevelKey@4$kCGNullDirectDisplay@0$kCGNullWindowID@0$kCGNumReservedBaseWindowLevels@5$kCGNumReservedWindowLevels@16$kCGNumberOfEventSuppressionStates@2$kCGNumberOfWindowLevelKeys@21$kCGOverlayWindowLevel@102$kCGOverlayWindowLevelKey@15$kCGPDFAllowsCommenting@64$kCGPDFAllowsContentAccessibility@32$kCGPDFAllowsContentCopying@16$kCGPDFAllowsDocumentAssembly@8$kCGPDFAllowsDocumentChanges@4$kCGPDFAllowsFormFieldEntry@128$kCGPDFAllowsHighQualityPrinting@2$kCGPDFAllowsLowQualityPrinting@1$kCGPDFArtBox@4$kCGPDFBleedBox@2$kCGPDFCropBox@1$kCGPDFMediaBox@0$kCGPDFObjectTypeArray@7$kCGPDFObjectTypeBoolean@2$kCGPDFObjectTypeDictionary@8$kCGPDFObjectTypeInteger@3$kCGPDFObjectTypeName@5$kCGPDFObjectTypeNull@1$kCGPDFObjectTypeReal@4$kCGPDFObjectTypeStream@9$kCGPDFObjectTypeString@6$kCGPDFTrimBox@3$kCGPathEOFill@1$kCGPathEOFillStroke@4$kCGPathElementAddCurveToPoint@3$kCGPathElementAddLineToPoint@1$kCGPathElementAddQuadCurveToPoint@2$kCGPathElementCloseSubpath@4$kCGPathElementMoveToPoint@0$kCGPathFill@0$kCGPathFillStroke@3$kCGPathStroke@2$kCGPatternTilingConstantSpacing@2$kCGPatternTilingConstantSpacingMinimalDistortion@1$kCGPatternTilingNoDistortion@0$kCGPopUpMenuWindowLevel@101$kCGPopUpMenuWindowLevelKey@11$kCGRenderingIntentAbsoluteColorimetric@1$kCGRenderingIntentDefault@0$kCGRenderingIntentPerceptual@3$kCGRenderingIntentRelativeColorimetric@2$kCGRenderingIntentSaturation@4$kCGScreenSaverWindowLevel@1000$kCGScreenSaverWindowLevelKey@13$kCGScreenUpdateOperationMove@1$kCGScreenUpdateOperationReducedDirtyRectangleCount@2147483648$kCGScreenUpdateOperationRefresh@0$kCGScrollEventUnitLine@1$kCGScrollEventUnitPixel@0$kCGScrollPhaseBegan@1$kCGScrollPhaseCancelled@8$kCGScrollPhaseChanged@2$kCGScrollPhaseEnded@4$kCGScrollPhaseMayBegin@128$kCGScrollWheelEventAcceleratedDeltaAxis1@176$kCGScrollWheelEventAcceleratedDeltaAxis2@175$kCGScrollWheelEventDeltaAxis1@11$kCGScrollWheelEventDeltaAxis2@12$kCGScrollWheelEventDeltaAxis3@13$kCGScrollWheelEventFixedPtDeltaAxis1@93$kCGScrollWheelEventFixedPtDeltaAxis2@94$kCGScrollWheelEventFixedPtDeltaAxis3@95$kCGScrollWheelEventInstantMouser@14$kCGScrollWheelEventIsContinuous@88$kCGScrollWheelEventMomentumOptionPhase@173$kCGScrollWheelEventMomentumPhase@123$kCGScrollWheelEventPointDeltaAxis1@96$kCGScrollWheelEventPointDeltaAxis2@97$kCGScrollWheelEventPointDeltaAxis3@98$kCGScrollWheelEventRawDeltaAxis1@178$kCGScrollWheelEventRawDeltaAxis2@177$kCGScrollWheelEventScrollCount@100$kCGScrollWheelEventScrollPhase@99$kCGSessionEventTap@1$kCGStatusWindowLevel@25$kCGStatusWindowLevelKey@9$kCGTabletEventDeviceID@24$kCGTabletEventPointButtons@18$kCGTabletEventPointPressure@19$kCGTabletEventPointX@15$kCGTabletEventPointY@16$kCGTabletEventPointZ@17$kCGTabletEventRotation@22$kCGTabletEventTangentialPressure@23$kCGTabletEventTiltX@20$kCGTabletEventTiltY@21$kCGTabletEventVendor1@25$kCGTabletEventVendor2@26$kCGTabletEventVendor3@27$kCGTabletProximityEventCapabilityMask@36$kCGTabletProximityEventDeviceID@31$kCGTabletProximityEventEnterProximity@38$kCGTabletProximityEventPointerID@30$kCGTabletProximityEventPointerType@37$kCGTabletProximityEventSystemTabletID@32$kCGTabletProximityEventTabletID@29$kCGTabletProximityEventVendorID@28$kCGTabletProximityEventVendorPointerSerialNumber@34$kCGTabletProximityEventVendorPointerType@33$kCGTabletProximityEventVendorUniqueID@35$kCGTailAppendEventTap@1$kCGTextClip@7$kCGTextFill@0$kCGTextFillClip@4$kCGTextFillStroke@2$kCGTextFillStrokeClip@6$kCGTextInvisible@3$kCGTextStroke@1$kCGTextStrokeClip@5$kCGToneMappingDefault@0$kCGToneMappingEXRGamma@4$kCGToneMappingITURecommended@3$kCGToneMappingImageSpecificLumaScaling@1$kCGToneMappingNone@5$kCGToneMappingReferenceWhiteBased@2$kCGTornOffMenuWindowLevel@3$kCGTornOffMenuWindowLevelKey@6$kCGUtilityWindowLevel@19$kCGUtilityWindowLevelKey@17$kCGWindowImageBestResolution@8$kCGWindowImageBoundsIgnoreFraming@1$kCGWindowImageDefault@0$kCGWindowImageNominalResolution@16$kCGWindowImageOnlyShadows@4$kCGWindowImageShouldBeOpaque@2$kCGWindowListExcludeDesktopElements@16$kCGWindowListOptionAll@0$kCGWindowListOptionIncludingWindow@8$kCGWindowListOptionOnScreenAboveWindow@2$kCGWindowListOptionOnScreenBelowWindow@4$kCGWindowListOptionOnScreenOnly@1$kCGWindowSharingNone@0$kCGWindowSharingReadOnly@1$kCGWindowSharingReadWrite@2$"""
misc.update(
    {
        "CGWindowSharingType": NewType("CGWindowSharingType", int),
        "CGPDFBox": NewType("CGPDFBox", int),
        "CGMomentumScrollPhase": NewType("CGMomentumScrollPhase", int),
        "CGToneMapping": NewType("CGToneMapping", int),
        "CGImagePixelFormatInfo": NewType("CGImagePixelFormatInfo", int),
        "CGEventTapPlacement": NewType("CGEventTapPlacement", int),
        "CGEventField": NewType("CGEventField", int),
        "CGConfigureOption": NewType("CGConfigureOption", int),
        "CGMouseButton": NewType("CGMouseButton", int),
        "CGWindowImageOption": NewType("CGWindowImageOption", int),
        "CGLineJoin": NewType("CGLineJoin", int),
        "CGColorSpaceModel": NewType("CGColorSpaceModel", int),
        "CGEventTapOptions": NewType("CGEventTapOptions", int),
        "CGTextEncoding": NewType("CGTextEncoding", int),
        "CGFontPostScriptFormat": NewType("CGFontPostScriptFormat", int),
        "CGEventSourceStateID": NewType("CGEventSourceStateID", int),
        "CGPathElementType": NewType("CGPathElementType", int),
        "CGEventMouseSubtype": NewType("CGEventMouseSubtype", int),
        "CGDisplayStreamFrameStatus": NewType("CGDisplayStreamFrameStatus", int),
        "CGScrollEventUnit": NewType("CGScrollEventUnit", int),
        "CGEventFilterMask": NewType("CGEventFilterMask", int),
        "CGColorConversionInfoTransformType": NewType(
            "CGColorConversionInfoTransformType", int
        ),
        "CGBitmapInfo": NewType("CGBitmapInfo", int),
        "CGPDFAccessPermissions": NewType("CGPDFAccessPermissions", int),
        "CGPatternTiling": NewType("CGPatternTiling", int),
        "CGInterpolationQuality": NewType("CGInterpolationQuality", int),
        "CGScrollPhase": NewType("CGScrollPhase", int),
        "CGLineCap": NewType("CGLineCap", int),
        "CGGesturePhase": NewType("CGGesturePhase", int),
        "CGWindowListOption": NewType("CGWindowListOption", int),
        "CGBlendMode": NewType("CGBlendMode", int),
        "CGCaptureOptions": NewType("CGCaptureOptions", int),
        "CGTextDrawingMode": NewType("CGTextDrawingMode", int),
        "CGRectEdge": NewType("CGRectEdge", int),
        "CGEventSuppressionState": NewType("CGEventSuppressionState", int),
        "CGEventTapLocation": NewType("CGEventTapLocation", int),
        "CGPDFTagType": NewType("CGPDFTagType", int),
        "CGPDFObjectType": NewType("CGPDFObjectType", int),
        "CGImageAlphaInfo": NewType("CGImageAlphaInfo", int),
        "CGWindowLevelKey": NewType("CGWindowLevelKey", int),
        "CGGlyphDeprecatedEnum": NewType("CGGlyphDeprecatedEnum", int),
        "CGDisplayChangeSummaryFlags": NewType("CGDisplayChangeSummaryFlags", int),
        "CGPDFDataFormat": NewType("CGPDFDataFormat", int),
        "CIRenderDestinationAlphaMode": NewType("CIRenderDestinationAlphaMode", int),
        "CGGradientDrawingOptions": NewType("CGGradientDrawingOptions", int),
        "CGScreenUpdateOperation": NewType("CGScreenUpdateOperation", int),
        "CGEventFlags": NewType("CGEventFlags", int),
        "CGWindowBackingType": NewType("CGWindowBackingType", int),
        "CGError": NewType("CGError", int),
        "CGDisplayStreamUpdateRectType": NewType("CGDisplayStreamUpdateRectType", int),
        "CGColorRenderingIntent": NewType("CGColorRenderingIntent", int),
        "CGEventType": NewType("CGEventType", int),
        "CGPathDrawingMode": NewType("CGPathDrawingMode", int),
        "CGImageByteOrderInfo": NewType("CGImageByteOrderInfo", int),
    }
)
misc.update(
    {
        "CIContextOption": NewType("CIContextOption", str),
        "CIFormat": NewType("CIFormat", int),
        "CIRAWDecoderVersion": NewType("CIRAWDecoderVersion", str),
        "CIImageAutoAdjustmentOption": NewType("CIImageAutoAdjustmentOption", str),
        "CIImageRepresentationOption": NewType("CIImageRepresentationOption", str),
        "CIRAWFilterOption": NewType("CIRAWFilterOption", str),
        "CIImageOption": NewType("CIImageOption", str),
    }
)
misc.update(
    {
        "kCGDisplayModeIsTelevisionOutput": "kCGDisplayModeIsTelevisionOutput",
        "kCGSessionUserNameKey": "kCGSSessionUserNameKey",
        "kCGSessionOnConsoleKey": "kCGSSessionOnConsoleKey",
        "kCGDisplayIOFlags": "IOFlags",
        "kCGDisplayModeIsStretched": "kCGDisplayModeIsStretched",
        "kCGNotifyGUIConsoleSessionChanged": b"com.apple.coregraphics.GUIConsoleSessionChanged",
        "kCGSessionUserIDKey": "kCGSSessionUserIDKey",
        "kCGDisplayModeIsInterlaced": "kCGDisplayModeIsInterlaced",
        "kCGDisplayBlendSolidColor": 1.0,
        "kCGDisplayHeight": "Height",
        "kCGDisplayBitsPerSample": "BitsPerSample",
        "kCGDisplayBlendNormal": 0.0,
        "kCGMouseDownEventMaskingDeadSwitchTimeout": 60.0,
        "kCGMaxDisplayReservationInterval": 15.0,
        "kCGDisplayWidth": "Width",
        "kCGDisplaySamplesPerPixel": "SamplesPerPixel",
        "kCGNotifyEventTapRemoved": b"com.apple.coregraphics.eventTapRemoved",
        "kCGSessionConsoleSetKey": "kCGSSessionConsoleSetKey",
        "kCGDisplayRefreshRate": "RefreshRate",
        "kCGDisplayBytesPerRow": "kCGDisplayBytesPerRow",
        "kCGDisplayBitsPerPixel": "BitsPerPixel",
        "kCGDisplayModeUsableForDesktopGUI": "UsableForDesktopGUI",
        "kCGSessionLoginDoneKey": "kCGSessionLoginDoneKey",
        "kCGIODisplayModeID": "IODisplayModeID",
        "kCGDisplayMode": "Mode",
        "kCGNotifyEventTapAdded": b"com.apple.coregraphics.eventTapAdded",
        "kCGDisplayModeIsSafeForHardware": "kCGDisplayModeIsSafeForHardware",
        "kCGNotifyGUISessionUserChanged": b"com.apple.coregraphics.GUISessionUserChanged",
    }
)
functions = {
    "CGPDFDocumentGetVersion": (
        b"v^{CGPDFDocument=}^i^i",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CGContextAddArcToPoint": (b"v^{CGContext=}ddddd",),
    "CGRectIntersection": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGDataConsumerRetain": (b"^{CGDataConsumer=}^{CGDataConsumer=}",),
    "CGColorSpaceCreateDeviceCMYK": (
        b"^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGGradientGetTypeID": (b"Q",),
    "CGPathCreateCopy": (
        b"^{CGPath=}^{CGPath=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorConversionInfoCreate": (
        b"^{CGColorConversionInfo=}^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFPageGetDrawingTransform": (
        b"{CGAffineTransform=dddddd}^{CGPDFPage=}i{CGRect={CGPoint=dd}{CGSize=dd}}iB",
    ),
    "CGFontCanCreatePostScriptSubset": (
        b"B^{CGFont=}i",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetStrokeColorSpace": (b"v^{CGContext=}^{CGColorSpace=}",),
    "CGContextPathContainsPoint": (b"B^{CGContext=}{CGPoint=dd}i",),
    "CGAffineTransformRotate": (
        b"{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}d",
    ),
    "CGContextRelease": (b"v^{CGContext=}",),
    "CGPDFArrayGetStream": (
        b"B^{CGPDFArray=}Q^^{CGPDFStream=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGEventKeyboardSetUnicodeString": (
        b"v^{__CGEvent=}Q^T",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "n"}}},
    ),
    "CGDisplayModeGetRefreshRate": (b"d^{CGDisplayMode=}",),
    "CGShieldingWindowID": (b"II",),
    "CGColorSpaceCreateWithColorSyncProfile": (
        b"^{CGColorSpace=}^{ColorSyncProfile=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGAffineTransformMake": (b"{CGAffineTransform=dddddd}dddddd",),
    "CGWindowListCreateDescriptionFromArray": (
        b"^{__CFArray=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextAddLines": (
        b"v^{CGContext=}^{CGPoint=dd}Q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGContextSetTextPosition": (b"v^{CGContext=}dd",),
    "CGColorCreateSRGB": (
        b"^{CGColor=}dddd",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextGetTextPosition": (b"{CGPoint=dd}^{CGContext=}",),
    "CGPDFPageGetRotationAngle": (b"i^{CGPDFPage=}",),
    "CGContextGetPathBoundingBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGContext=}",),
    "CGRectContainsPoint": (b"B{CGRect={CGPoint=dd}{CGSize=dd}}{CGPoint=dd}",),
    "CGPDFDictionaryGetCount": (b"Q^{CGPDFDictionary=}",),
    "CGRectMake": (b"{CGRect={CGPoint=dd}{CGSize=dd}}dddd",),
    "CGColorSpaceRetain": (b"^{CGColorSpace=}^{CGColorSpace=}",),
    "CGPathCreateCopyByStrokingPath": (
        b"^{CGPath=}^{CGPath=}^{CGAffineTransform=dddddd}diid",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGContextAddEllipseInRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGContextEndTransparencyLayer": (b"v^{CGContext=}",),
    "CGContextSelectFont": (
        b"v^{CGContext=}^tdi",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "CGLayerGetSize": (b"{CGSize=dd}^{CGLayer=}",),
    "CGWindowListCreate": (
        b"^{__CFArray=}II",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGSizeEqualToSize": (b"B{CGSize=dd}{CGSize=dd}",),
    "CGColorConverterCreateSimple": (
        b"^{CGColorConversionInfo=}^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFStringCopyTextString": (
        b"^{__CFString=}^{CGPDFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorConversionInfoConvertData": (
        b"B^{CGColorConversionInfo=}QQ^v{CGColorBufferFormat=IIQQQ}^v{CGColorBufferFormat=IIQQQ}^{__CFDictionary=}",
        "",
        {"arguments": {3: {"c_array_of_variable_length": True, "type_modifier": "n"}}},
    ),
    "CGColorSpaceGetBaseColorSpace": (b"^{CGColorSpace=}^{CGColorSpace=}",),
    "CGPathCreateMutable": (
        b"^{CGPath=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPatternCreate": (
        b"^{CGPattern=}^v{CGRect={CGPoint=dd}{CGSize=dd}}{CGAffineTransform=dddddd}ddiB^{CGPatternCallbacks=I^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGBitmapContextGetHeight": (b"Q^{CGContext=}",),
    "CGPDFPageGetBoxRect": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFPage=}i",),
    "CGPDFStringCopyDate": (
        b"^{__CFDate=}^{CGPDFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayStreamUpdateGetDropCount": (b"Q^{CGDisplayStreamUpdate=}",),
    "CGDisplayBestModeForParametersAndRefreshRate": (
        selAorI(b"^{__CFDictionary=}IQQQd^i", b"^{__CFDictionary=}IQQQd^I"),
        "",
        {"arguments": {5: {"type_modifier": "o"}}},
    ),
    "CGPDFScannerPopString": (
        b"B^{CGPDFScanner=}^^{CGPDFString=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGPDFPageGetTypeID": (b"Q",),
    "CGContextAddRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGDataProviderCreateWithURL": (
        b"^{CGDataProvider=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFScannerCreate": (
        b"^{CGPDFScanner=}^{CGPDFContentStream=}^{CGPDFOperatorTable=}^v",
        "",
        {"retval": {"already_cfretained": False}},
    ),
    "CGConfigureDisplayFadeEffect": (b"i^{_CGDisplayConfigRef=}fffff",),
    "CGDisplayFade": (selAorI(b"iIffffffi", b"iIffffffI"),),
    "CGPDFArrayGetObject": (
        b"B^{CGPDFArray=}Q^^{CGPDFObject=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGEventSetType": (b"v^{__CGEvent=}I",),
    "CGDataProviderCreateWithFilename": (
        b"^{CGDataProvider=}^t",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {0: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CGColorGetComponents": (
        b"^d^{CGColor=}",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CGPDFContextSetOutline": (b"v^{CGContext=}^{__CFDictionary=}",),
    "CGAffineTransformMakeTranslation": (b"{CGAffineTransform=dddddd}dd",),
    "CGSizeMake": (b"{CGSize=dd}dd",),
    "CGDisplayVendorNumber": (b"II",),
    "CGPDFContextBeginTag": (b"v^{CGContext=}i^{__CFDictionary=}",),
    "CGPDFDocumentGetID": (b"^{CGPDFArray=}^{CGPDFDocument=}",),
    "CGDataProviderCreateWithData": (
        b"^{CGDataProvider=}^v^vQ^?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^v"},
                            2: {"type": b"Q"},
                        },
                    }
                }
            },
        },
    ),
    "CGColorSpaceCreatePattern": (
        b"^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSynchronize": (b"v^{CGContext=}",),
    "CGDisplayModeGetIODisplayModeID": (b"i^{CGDisplayMode=}",),
    "CGFontGetGlyphBBoxes": (
        b"B^{CGFont=}^SQ^{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                3: {"c_array_length_in_arg": 2, "type_modifier": "o"},
            }
        },
    ),
    "CGPaletteCreateWithByteSamples": (
        b"^{_CGDirectPaletteRef=}^{CGDeviceByteColor=CCC}I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}},
        },
    ),
    "CGPDFContentStreamGetResource": (
        b"^{CGPDFObject=}^{CGPDFContentStream=}^t^t",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"c_array_delimited_by_null": True, "type_modifier": "n"},
            }
        },
    ),
    "CGAffineTransformMakeRotation": (b"{CGAffineTransform=dddddd}d",),
    "CGGradientRetain": (b"^{CGGradient=}^{CGGradient=}",),
    "CGPDFContextSetIDTree": (b"v^{CGContext=}^{CGPDFDictionary=}",),
    "CGPaletteCreateWithDisplay": (
        b"^{_CGDirectPaletteRef=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayCreateImageForRect": (
        b"^{CGImage=}I{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGImageGetWidth": (b"Q^{CGImage=}",),
    "CGPDFDocumentIsUnlocked": (b"B^{CGPDFDocument=}",),
    "CGPathCreateWithRect": (
        b"^{CGPath=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGAffineTransform=dddddd}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGImageGetBitmapInfo": (b"I^{CGImage=}",),
    "CGEventSourceSetKeyboardType": (b"v^{__CGEventSource=}I",),
    "CGDataProviderGetInfo": (b"^v^{CGDataProvider=}",),
    "CGContextSetAllowsFontSmoothing": (b"v^{CGContext=}B",),
    "CGDisplayUsesOpenGLAcceleration": (selAorI(b"iI", b"II"),),
    "CGPointMakeWithDictionaryRepresentation": (
        b"B^{__CFDictionary=}^{CGPoint=dd}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGContextResetClip": (b"v^{CGContext=}",),
    "CGPDFDictionaryApplyFunction": (
        b"v^{CGPDFDictionary=}^?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {
                                "type": b"^t",
                                "c_array_delimeted_by_null": True,
                                "type_modifier": "n",
                            },
                            1: {"type": b"^{CGPDFObject=}"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGWindowServerCreateServerPort": (
        b"^{__CFMachPort=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathAddEllipseInRect": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGColorSpaceGetColorTableCount": (b"Q^{CGColorSpace=}",),
    "CGWindowListCreateImage": (
        b"^{CGImage=}{CGRect={CGPoint=dd}{CGSize=dd}}III",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathCreateCopyByTransformingPath": (
        b"^{CGPath=}^{CGPath=}^{CGAffineTransform=dddddd}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGContextClearRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGPDFDocumentGetAccessPermissions": (b"I^{CGPDFDocument=}",),
    "CGBitmapContextGetBitmapInfo": (b"I^{CGContext=}",),
    "CGPathAddQuadCurveToPoint": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}dddd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGColorSpaceCreateDeviceGray": (
        b"^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGRectIntersectsRect": (
        b"B{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGPDFPageGetDocument": (b"^{CGPDFDocument=}^{CGPDFPage=}",),
    "CGRestorePermanentDisplayConfiguration": (b"v",),
    "CGImageGetTypeID": (b"Q",),
    "CGFontCreatePostScriptEncoding": (
        b"^{__CFData=}^{CGFont=}^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_of_fixed_length": 256, "type_modifier": "n"}},
        },
    ),
    "CGAffineTransformMakeWithComponents": (
        b"{CGAffineTransform=dddddd}{CGAffineTransformComponents={CGSize=dd}dd{CGVector=dd}}",
    ),
    "CGPointApplyAffineTransform": (
        b"{CGPoint=dd}{CGPoint=dd}{CGAffineTransform=dddddd}",
    ),
    "CGPDFContextSetParentTree": (b"v^{CGContext=}^{CGPDFDictionary=}",),
    "CGEventSourceGetSourceStateID": (b"i^{__CGEventSource=}",),
    "CGRectStandardize": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGPathAddLineToPoint": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}dd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGDataProviderCopyData": (
        b"^{__CFData=}^{CGDataProvider=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorCreateGenericGray": (
        b"^{CGColor=}dd",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorSpaceIsWideGamutRGB": (b"B^{CGColorSpace=}",),
    "CGPDFContextSetURLForRect": (
        b"v^{CGContext=}^{__CFURL=}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGColorRetain": (b"^{CGColor=}^{CGColor=}",),
    "CGColorCreateGenericCMYK": (
        b"^{CGColor=}ddddd",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGGLContextCreate": (
        b"^{CGContext=}^v{CGSize=dd}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGBeginDisplayConfiguration": (
        b"i^^{_CGDisplayConfigRef=}",
        "",
        {"arguments": {0: {"type_modifier": "o"}}},
    ),
    "CGDisplayStreamGetTypeID": (b"Q",),
    "CGBitmapContextGetBitsPerPixel": (b"Q^{CGContext=}",),
    "CGPDFDictionaryGetArray": (
        b"B^{CGPDFDictionary=}^t^^{CGPDFArray=}",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGColorSpaceCreateWithPlatformColorSpace": (
        b"^{CGColorSpace=}^v",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetCMYKStrokeColor": (b"v^{CGContext=}ddddd",),
    "CGDisplayStreamGetRunLoopSource": (b"^{__CFRunLoopSource=}^{CGDisplayStream=}",),
    "CGContextEndPage": (b"v^{CGContext=}",),
    "CGUnregisterScreenRefreshCallback": (
        b"v^?^v",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"^{CGRect={CGPoint=dd}{CGSize=dd}}"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGPathAddRect": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGPDFContentStreamRelease": (b"v^{CGPDFContentStream=}",),
    "CGPathApplyWithBlock": (
        b"v^{CGPath=}@?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"typestr": "^v"},
                            1: {"typestr": "^{CGPathElement=}"},
                        },
                    }
                }
            }
        },
    ),
    "CGContextGetCTM": (b"{CGAffineTransform=dddddd}^{CGContext=}",),
    "CGDisplayStreamUpdateGetRects": (
        b"^{CGRect={CGPoint=dd}{CGSize=dd}}^{CGDisplayStreamUpdate=}i^Q",
        "",
        {
            "retval": {"c_array_length_in_arg": 2},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "CGPDFArrayGetName": (
        b"B^{CGPDFArray=}Q^^t",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGEventSourceGetPixelsPerLine": (b"d^{__CGEventSource=}",),
    "CGDisplayStreamUpdateGetMovedRectsDelta": (
        b"v^{CGDisplayStreamUpdate=}^d^d",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CGRectGetHeight": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGEventSourceGetTypeID": (b"Q",),
    "CGPDFDictionaryGetStream": (
        b"B^{CGPDFDictionary=}^t^^{CGPDFStream=}",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGDataProviderCreateWithCFData": (
        b"^{CGDataProvider=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGShieldingWindowLevel": (b"i",),
    "CGDisplaySetPalette": (b"iI^{_CGDirectPaletteRef=}",),
    "CGDisplayIsOnline": (selAorI(b"iI", b"II"),),
    "CGDisplayStreamCreate": (
        b"^{CGDisplayStream=}IQQi^{__CFDictionary=}@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "I"},
                            2: {"type": "Q"},
                            3: {"type": "@"},
                            4: {"type": "@"},
                        },
                    }
                }
            },
        },
    ),
    "CGFontGetCapHeight": (b"i^{CGFont=}",),
    "CGContextShowGlyphsWithAdvances": (
        b"v^{CGContext=}^S^{CGSize=dd}Q",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
            }
        },
    ),
    "CGDataConsumerCreate": (
        b"^{CGDataConsumer=}^v^{CGDataConsumerCallbacks=^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFArrayGetInteger": (
        b"B^{CGPDFArray=}Q^q",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGPostScrollWheelEvent": (
        b"iIi",
        "",
        {"c_array_length_in_arg": 0, "variadic": True},
    ),
    "CGColorCreateCopy": (
        b"^{CGColor=}^{CGColor=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGBitmapContextCreate": (
        b"^{CGContext=}^vQQQQ^{CGColorSpace=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFArrayApplyBlock": (
        b"v^{CGPDFArray=}@?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"B"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "l"},
                            2: {"type": "^{CGPDFObject=}"},
                            3: {"type": "^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGPathAddRelativeArc": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}ddddd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGDisplaySetStereoOperation": (selAorI(b"iIiiI", b"iIIII"),),
    "CGPaletteIsEqualToPalette": (b"B^{_CGDirectPaletteRef=}^{_CGDirectPaletteRef=}",),
    "CGContextSetShouldAntialias": (b"v^{CGContext=}B",),
    "CGDisplayModeGetHeight": (b"Q^{CGDisplayMode=}",),
    "CGContextSetFillColor": (
        b"v^{CGContext=}^d",
        "",
        {"arguments": {1: {"c_array_of_variable_length": True, "type_modifier": "n"}}},
    ),
    "CGImageRelease": (b"v^{CGImage=}",),
    "CGInhibitLocalEvents": (selAorI(b"ii", b"iI"),),
    "CGContextSetGrayFillColor": (b"v^{CGContext=}dd",),
    "CGColorSpaceIsHDR": (b"B^{CGColorSpace=}",),
    "CGImageGetUTType": (b"^{__CFString=}^{CGImage=}",),
    "CGPSConverterCreate": (
        b"^{CGPSConverter=}^v^{CGPSConverterCallbacks=I^?^?^?^?^?^?^?}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDirectDisplayCopyCurrentMetalDevice": (
        b"@I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextClipToMask": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGImage=}",
    ),
    "CGDisplayCopyColorSpace": (
        b"^{CGColorSpace=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextAddLineToPoint": (b"v^{CGContext=}dd",),
    "CGEventSourceGetLocalEventsSuppressionInterval": (b"d^{__CGEventSource=}",),
    "CGColorSpaceGetTypeID": (b"Q",),
    "CGPathAddPath": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}^{CGPath=}",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGDataProviderRetain": (b"^{CGDataProvider=}^{CGDataProvider=}",),
    "CGEventCreateFromData": (
        b"^{__CGEvent=}^{__CFAllocator=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayPixelsHigh": (b"QI",),
    "CGImageCreateCopyWithContentHeadroom": (
        b"^{CGImage=}f^{CGImage=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGConfigureDisplayStereoOperation": (
        selAorI(b"i^{_CGDisplayConfigRef=}Iii", b"i^{_CGDisplayConfigRef=}III"),
    ),
    "CGFontGetNumberOfGlyphs": (b"Q^{CGFont=}",),
    "CGPDFOperatorTableCreate": (
        b"^{CGPDFOperatorTable=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFContextAddDestinationAtPoint": (b"v^{CGContext=}^{__CFString=}{CGPoint=dd}",),
    "CGPDFScannerGetContentStream": (b"^{CGPDFContentStream=}^{CGPDFScanner=}",),
    "CGContextSetShouldSubpixelQuantizeFonts": (b"v^{CGContext=}B",),
    "CGPathContainsPoint": (
        b"B^{CGPath=}^{CGAffineTransform=dddddd}{CGPoint=dd}B",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGSizeApplyAffineTransform": (
        b"{CGSize=dd}{CGSize=dd}{CGAffineTransform=dddddd}",
    ),
    "CGRectIntegral": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGDisplayPrimaryDisplay": (b"II",),
    "CGContextConcatCTM": (b"v^{CGContext=}{CGAffineTransform=dddddd}",),
    "CGFunctionRelease": (b"v^{CGFunction=}",),
    "CGPDFDocumentGetOutline": (b"^{__CFDictionary=}^{CGPDFDocument=}",),
    "CGWindowListCreateImageFromArray": (
        b"^{CGImage=}{CGRect={CGPoint=dd}{CGSize=dd}}^{__CFArray=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPatternRetain": (b"^{CGPattern=}^{CGPattern=}",),
    "CGPaletteCreateWithSamples": (
        b"^{_CGDirectPaletteRef=}^{CGDeviceColor=fff}I",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {0: {"c_array_length_in_arg": 1, "type_modifier": "n"}},
        },
    ),
    "CGDataProviderGetTypeID": (b"Q",),
    "CGPaletteCreateWithCapacity": (
        b"^{_CGDirectPaletteRef=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayBytesPerRow": (b"QI",),
    "CGSetLocalEventsSuppressionInterval": (b"id",),
    "CGPDFArrayGetCount": (b"Q^{CGPDFArray=}",),
    "CGPDFContextClose": (b"v^{CGContext=}",),
    "CGDisplayIsBuiltin": (selAorI(b"iI", b"II"),),
    "CGContextIsPathEmpty": (b"B^{CGContext=}",),
    "CGContextSetShadow": (b"v^{CGContext=}{CGSize=dd}d",),
    "CGPathGetBoundingBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPath=}",),
    "CGColorGetNumberOfComponents": (b"Q^{CGColor=}",),
    "CGColorSpaceRelease": (b"v^{CGColorSpace=}",),
    "CGGetDisplayTransferByTable": (
        b"iII^f^f^f^I",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": (1, 5), "type_modifier": "o"},
                3: {"c_array_length_in_arg": (1, 5), "type_modifier": "o"},
                4: {"c_array_length_in_arg": (1, 5), "type_modifier": "o"},
                5: {"type_modifier": "o"},
            }
        },
    ),
    "CGImageShouldToneMap": (b"B^{CGImage=}",),
    "CGPathCreateCopyOfLineByIntersectingPath": (
        b"^{CGPath=}^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextShowGlyphsAtPoint": (
        b"v^{CGContext=}dd^SQ",
        "",
        {"arguments": {3: {"c_array_length_in_arg": 4, "type_modifier": "n"}}},
    ),
    "CGPathIntersectsPath": (b"B^{CGPath=}^{CGPath=}B",),
    "CGPathAddLines": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}^{CGPoint=dd}Q",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
            }
        },
    ),
    "CGColorCreateGenericRGB": (
        b"^{CGColor=}dddd",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextDrawPDFPage": (b"v^{CGContext=}^{CGPDFPage=}",),
    "CGDisplayModeRetain": (b"^{CGDisplayMode=}^{CGDisplayMode=}",),
    "CGDisplayGammaTableCapacity": (b"II",),
    "CGFontCreateWithFontName": (
        b"^{CGFont=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayCopyAllDisplayModes": (
        b"^{__CFArray=}I^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextScaleCTM": (b"v^{CGContext=}dd",),
    "CGRectDivide": (
        b"v{CGRect={CGPoint=dd}{CGSize=dd}}^{CGRect={CGPoint=dd}{CGSize=dd}}^{CGRect={CGPoint=dd}{CGSize=dd}}dI",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CGContextSetLineCap": (b"v^{CGContext=}i",),
    "CGImageMaskCreate": (
        b"^{CGImage=}QQQQQ^{CGDataProvider=}^dB",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                6: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGContextDrawRadialGradient": (
        b"v^{CGContext=}^{CGGradient=}{CGPoint=dd}d{CGPoint=dd}dI",
    ),
    "CGFontCopyVariations": (
        b"^{__CFDictionary=}^{CGFont=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGConfigureDisplayMirrorOfDisplay": (b"i^{_CGDisplayConfigRef=}II",),
    "CGEventSourceCreate": (
        b"^{__CGEventSource=}i",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPreflightListenEventAccess": (b"B",),
    "CGEventGetTimestamp": (b"Q^{__CGEvent=}",),
    "CGContextFillEllipseInRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGContextSetAlpha": (b"v^{CGContext=}d",),
    "CGContextAddQuadCurveToPoint": (b"v^{CGContext=}dddd",),
    "CGColorSpaceGetNumberOfComponents": (b"Q^{CGColorSpace=}",),
    "CGPathCreateCopyByFlattening": (
        b"^{CGPath=}^{CGPath=}d",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGEventCreateKeyboardEvent": (
        b"^{__CGEvent=}^{__CGEventSource=}SB",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFDocumentGetMediaBox": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFDocument=}i",
    ),
    "CGEventSetSource": (b"v^{__CGEvent=}^{__CGEventSource=}",),
    "CGSetDisplayTransferByByteTable": (
        b"iII^z^z^z",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": 1, "type_modifier": "n"},
                3: {"c_array_length_in_arg": 1, "type_modifier": "n"},
                4: {"c_array_length_in_arg": 1, "type_modifier": "n"},
            }
        },
    ),
    "CGPDFDictionaryGetString": (
        b"B^{CGPDFDictionary=}^t^^{CGPDFString=}",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGRegisterScreenRefreshCallback": (
        b"i^?^v",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"^{CGRect={CGPoint=dd}{CGSize=dd}}"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGFontGetUnitsPerEm": (b"i^{CGFont=}",),
    "CGContextEOClip": (b"v^{CGContext=}",),
    "CGAcquireDisplayFadeReservation": (
        b"if^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGBitmapContextGetData": (
        b"^v^{CGContext=}",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CGAffineTransformIsIdentity": (b"B{CGAffineTransform=dddddd}",),
    "CGContextGetInterpolationQuality": (b"i^{CGContext=}",),
    "CGPathGetPathBoundingBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPath=}",),
    "CGContextRotateCTM": (b"v^{CGContext=}d",),
    "CGImageCreateCopy": (
        b"^{CGImage=}^{CGImage=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGImageGetShouldInterpolate": (b"B^{CGImage=}",),
    "CGContextStrokeRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGImageGetDecode": (
        b"^d^{CGImage=}",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CGColorCreateCopyByMatchingToColorSpace": (
        b"^{CGColor=}^{CGColorSpace=}i^{CGColor=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetAllowsAntialiasing": (b"v^{CGContext=}B",),
    "CGPDFScannerPopDictionary": (
        b"B^{CGPDFScanner=}^^{CGPDFDictionary=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGRectGetMidX": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGFontCopyTableForTag": (
        b"^{__CFData=}^{CGFont=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGEventSourceGetLocalEventsFilterDuringSuppressionState": (
        b"I^{__CGEventSource=}I",
    ),
    "CGColorGetTypeID": (b"Q",),
    "CGSetDisplayTransferByFormula": (b"iIfffffffff",),
    "CGPDFStreamGetDictionary": (b"^{CGPDFDictionary=}^{CGPDFStream=}",),
    "CGEventSourceSetLocalEventsFilterDuringSuppressionState": (
        b"v^{__CGEventSource=}II",
    ),
    "CGRectContainsRect": (
        b"B{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGColorGetPattern": (b"^{CGPattern=}^{CGColor=}",),
    "CGPaletteCreateDefaultColorPalette": (
        b"^{_CGDirectPaletteRef=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGFontCreatePostScriptSubset": (
        b"^{__CFData=}^{CGFont=}^{__CFString=}i^SQ^S",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {"c_array_length_in_arg": 4, "type_modifier": "n"},
                5: {"type_modifier": "n"},
            },
        },
    ),
    "CGPDFDocumentGetCatalog": (b"^{CGPDFDictionary=}^{CGPDFDocument=}",),
    "CGColorSpaceGetModel": (b"i^{CGColorSpace=}",),
    "CGImageGetColorSpace": (b"^{CGColorSpace=}^{CGImage=}",),
    "CGPDFArrayGetString": (
        b"B^{CGPDFArray=}Q^^{CGPDFString=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGPointMake": (b"{CGPoint=dd}dd",),
    "CGPaletteRelease": (b"v^{_CGDirectPaletteRef=}",),
    "CGPDFDictionaryGetObject": (
        b"B^{CGPDFDictionary=}^t^^{CGPDFObject=}",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGEventTapCreateForPSN": (
        b"^{__CFMachPort=}^vIIQ^?^v",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"^{__CGEvent=}"},
                        "arguments": {
                            0: {"type": b"^{__CGEventTapProxy=}"},
                            1: {"type": b"I"},
                            2: {"type": b"^{__CGEvent=}"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CGReleaseDisplayFadeReservation": (b"iI",),
    "CGPathCreateCopyByNormalizing": (
        b"^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayRegisterReconfigurationCallback": (
        b"i^?^v",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGPDFPageRetain": (b"^{CGPDFPage=}^{CGPDFPage=}",),
    "CGLayerCreateWithContext": (
        b"^{CGLayer=}^{CGContext=}{CGSize=dd}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextStrokeLineSegments": (
        b"v^{CGContext=}^{CGPoint=dd}Q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGImageIsMask": (b"B^{CGImage=}",),
    "CGPDFObjectGetType": (b"i^{CGPDFObject=}",),
    "CGEventSourceGetUserData": (b"q^{__CGEventSource=}",),
    "CGContextSetStrokeColor": (
        b"v^{CGContext=}^d",
        "",
        {"arguments": {1: {"c_array_of_variable_length": True, "type_modifier": "n"}}},
    ),
    "CGPDFContextEndTag": (b"v^{CGContext=}",),
    "CGPDFScannerPopBoolean": (
        b"B^{CGPDFScanner=}^C",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGGradientRelease": (b"v^{CGGradient=}",),
    "CGConfigureDisplayMode": (b"i^{_CGDisplayConfigRef=}I^{__CFDictionary=}",),
    "CGWarpMouseCursorPosition": (b"i{CGPoint=dd}",),
    "CGPathCreateWithRoundedRect": (
        b"^{CGPath=}{CGRect={CGPoint=dd}{CGSize=dd}}dd^{CGAffineTransform=dddddd}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "n"}},
        },
    ),
    "CGPDFDocumentGetInfo": (b"^{CGPDFDictionary=}^{CGPDFDocument=}",),
    "CGContextSetStrokePattern": (
        b"v^{CGContext=}^{CGPattern=}^d",
        "",
        {"arguments": {2: {"c_array_of_variable_length": True, "type_modifier": "n"}}},
    ),
    "CGDisplayCanSetPalette": (b"II",),
    "CGRectApplyAffineTransform": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}{CGAffineTransform=dddddd}",
    ),
    "CGEventSetDoubleValueField": (b"v^{__CGEvent=}Id",),
    "CGContextFlush": (b"v^{CGContext=}",),
    "CGWindowListCopyWindowInfo": (
        b"^{__CFArray=}II",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGFontGetXHeight": (b"i^{CGFont=}",),
    "CGPDFContextCreate": (
        b"^{CGContext=}^{CGDataConsumer=}^{CGRect={CGPoint=dd}{CGSize=dd}}^{__CFDictionary=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGPaletteGetIndexForColor": (b"I^{_CGDirectPaletteRef=}{CGDeviceColor=fff}",),
    "CGImageCreateWithJPEGDataProvider": (
        b"^{CGImage=}^{CGDataProvider=}^dBi",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGPDFDocumentCreateWithURL": (
        b"^{CGPDFDocument=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathAddArcToPoint": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}ddddd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGVectorMake": (b"{CGVector=dd}dd",),
    "CGDisplayIsActive": (selAorI(b"iI", b"II"),),
    "CGPDFScannerScan": (b"B^{CGPDFScanner=}",),
    "CGPathCreateMutableCopyByTransformingPath": (
        b"^{CGPath=}^{CGPath=}^{CGAffineTransform=dddddd}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGSetDisplayTransferByTable": (
        b"iII^f^f^f",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": 1, "type_modifier": "n"},
                3: {"c_array_length_in_arg": 1, "type_modifier": "n"},
                4: {"c_array_length_in_arg": 1, "type_modifier": "n"},
            }
        },
    ),
    "CGFontGetDescent": (b"i^{CGFont=}",),
    "CGImageGetPixelFormatInfo": (b"I^{CGImage=}",),
    "CGImageGetRenderingIntent": (b"i^{CGImage=}",),
    "CGGLContextUpdateViewportSize": (b"v^{CGContext=}{CGSize=dd}",),
    "CGGetEventTapList": (
        b"iI^{__CGEventTapInformation=IIIQiiBfff}^I",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": (0, 2), "type_modifier": "o"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGEnableEventStateCombining": (selAorI(b"ii", b"iI"),),
    "CGColorSpaceCreateDeviceRGB": (
        b"^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathEqualToPath": (b"B^{CGPath=}^{CGPath=}",),
    "CGPDFScannerPopObject": (
        b"B^{CGPDFScanner=}^^{CGPDFObject=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGDisplayIsCaptured": (selAorI(b"iI", b"II"),),
    "CGPDFPageRelease": (b"v^{CGPDFPage=}",),
    "CGDisplayStreamStart": (b"i^{CGDisplayStream=}",),
    "CGRectIsEmpty": (b"B{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGDisplayMoveCursorToPoint": (b"iI{CGPoint=dd}",),
    "CGPDFScannerPopInteger": (
        b"B^{CGPDFScanner=}^q",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGPaletteGetNumberOfSamples": (b"I^{_CGDirectPaletteRef=}",),
    "CGContextStrokePath": (b"v^{CGContext=}",),
    "CGEventSetLocation": (b"v^{__CGEvent=}{CGPoint=dd}",),
    "CGColorSpaceIsPQBased": (b"B^{CGColorSpace=}",),
    "CGAffineTransformScale": (
        b"{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}dd",
    ),
    "CGEventSourceSetUserData": (b"v^{__CGEventSource=}q",),
    "CGAffineTransformDecompose": (
        b"{CGAffineTransformComponents={CGSize=dd}dd{CGVector=dd}}{CGAffineTransform=dddddd}",
    ),
    "CGColorSpaceCreateExtended": (
        b"^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFArrayGetArray": (
        b"B^{CGPDFArray=}Q^^{CGPDFArray=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGContextDrawLayerInRect": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGLayer=}",
    ),
    "CGDataProviderRelease": (b"v^{CGDataProvider=}",),
    "CGEventPost": (b"vI^{__CGEvent=}",),
    "CGFontCreateCopyWithVariations": (
        b"^{CGFont=}^{CGFont=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGMainDisplayID": (b"I",),
    "CGFontGetTypeID": (b"Q",),
    "CGPathAddRoundedRect": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}{CGRect={CGPoint=dd}{CGSize=dd}}dd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGRectEqualToRect": (
        b"B{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGRectGetMaxY": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGBitmapContextGetWidth": (b"Q^{CGContext=}",),
    "CGShadingCreateRadial": (
        b"^{CGShading=}^{CGColorSpace=}{CGPoint=dd}d{CGPoint=dd}d^{CGFunction=}BB",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFScannerRetain": (b"^{CGPDFScanner=}^{CGPDFScanner=}",),
    "CGDisplayMirrorsDisplay": (b"II",),
    "CGPDFContextSetPageTagStructureTree": (b"v^{CGContext=}^{__CFDictionary=}",),
    "CGContextAddRects": (
        b"v^{CGContext=}^{CGRect={CGPoint=dd}{CGSize=dd}}Q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGDataConsumerCreateWithURL": (
        b"^{CGDataConsumer=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextConvertRectToUserSpace": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGGradientCreateWithColors": (
        b"^{CGGradient=}^{CGColorSpace=}^{__CFArray=}^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "n"}},
        },
    ),
    "CGPathApply": (
        b"v^{CGPath=}^v^?",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{CGPathElement=i^{CGPoint=dd}}"},
                        },
                    }
                }
            }
        },
    ),
    "CGGetDisplaysWithOpenGLDisplayMask": (
        b"iII^I^I",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": (1, 3), "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CGImageRetain": (b"^{CGImage=}^{CGImage=}",),
    "CGContextAddArc": (b"v^{CGContext=}dddddi",),
    "CGFontCreateWithPlatformFont": (
        b"^{CGFont=}^v",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorSpaceCreateExtendedLinearized": (
        b"^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFContentStreamRetain": (b"^{CGPDFContentStream=}^{CGPDFContentStream=}",),
    "CGErrorSetCallback": (
        b"v^?",
        "",
        {
            "arguments": {
                0: {
                    "callable": {"retval": {"type": b"v"}, "arguments": {}},
                    "callable_retained": True,
                }
            }
        },
    ),
    "CGCompleteDisplayConfiguration": (b"i^{_CGDisplayConfigRef=}I",),
    "CGAffineTransformTranslate": (
        b"{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}dd",
    ),
    "CGContextAddCurveToPoint": (b"v^{CGContext=}dddddd",),
    "CGPDFContentStreamCreateWithPage": (
        b"^{CGPDFContentStream=}^{CGPDFPage=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGEventSourceGetKeyboardType": (b"I^{__CGEventSource=}",),
    "CGEventKeyboardGetUnicodeString": (
        b"v^{__CGEvent=}Q^Q^T",
        "",
        {
            "arguments": {
                2: {"type_modifier": "o"},
                3: {"c_array_length_in_arg": (1, 2), "type_modifier": "o"},
            }
        },
    ),
    "CGDisplaySwitchToMode": (b"iI^{__CFDictionary=}",),
    "CGEventSetIntegerValueField": (b"v^{__CGEvent=}Iq",),
    "CGEventCreate": (
        b"^{__CGEvent=}^{__CGEventSource=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetFillColorSpace": (b"v^{CGContext=}^{CGColorSpace=}",),
    "CGFontGetGlyphWithGlyphName": (b"S^{CGFont=}^{__CFString=}",),
    "CGFunctionRetain": (b"^{CGFunction=}^{CGFunction=}",),
    "CGContextConvertPointToUserSpace": (b"{CGPoint=dd}^{CGContext=}{CGPoint=dd}",),
    "CGColorSpaceCopyName": (
        b"^{__CFString=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPatternRelease": (b"v^{CGPattern=}",),
    "CGPointEqualToPoint": (b"B{CGPoint=dd}{CGPoint=dd}",),
    "CGCursorIsDrawnInFramebuffer": (selAorI(b"i", b"I"),),
    "CGEventSourceCounterForEventType": (b"IiI",),
    "CGDisplayCaptureWithOptions": (b"iII",),
    "CGDisplayIsStereo": (selAorI(b"iI", b"II"),),
    "CGPDFTagTypeGetName": (
        b"^ti",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "CGBitmapContextGetBytesPerRow": (b"Q^{CGContext=}",),
    "CGContextBeginTransparencyLayer": (b"v^{CGContext=}^{__CFDictionary=}",),
    "CGEventPostToPSN": (
        b"v^{ProcessSerialNumber=II}^{__CGEvent=}",
        "",
        {"arguments": {0: {"type_modifier": "n"}}},
    ),
    "CGFontRetain": (b"^{CGFont=}^{CGFont=}",),
    "CGContextSetLineDash": (
        b"v^{CGContext=}d^dQ",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 3, "type_modifier": "n"}}},
    ),
    "CGColorSpaceCreateICCBased": (
        b"^{CGColorSpace=}Q^d^{CGDataProvider=}^{CGColorSpace=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGContextSetGrayStrokeColor": (b"v^{CGContext=}dd",),
    "CGPDFOperatorTableRelease": (b"v^{CGPDFOperatorTable=}",),
    "CGContextGetTypeID": (b"Q",),
    "CGRectOffset": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}dd",
    ),
    "CGLayerGetTypeID": (b"Q",),
    "CGColorSpaceCreateCalibratedGray": (
        b"^{CGColorSpace=}^d^dd",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
                1: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
            },
        },
    ),
    "CGEventTapCreate": (
        b"^{__CFMachPort=}IIIQ^?^v",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"^{__CGEvent=}"},
                        "arguments": {
                            0: {"type": b"^{__CGEventTapProxy=}"},
                            1: {"type": b"I"},
                            2: {"type": b"^{__CGEvent=}"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CGContextSetRenderingIntent": (b"v^{CGContext=}i",),
    "CGColorSpaceIsHLGBased": (b"B^{CGColorSpace=}",),
    "CGDisplayCurrentMode": (b"^{__CFDictionary=}I",),
    "CGConfigureDisplayWithDisplayMode": (
        b"i^{_CGDisplayConfigRef=}I^{CGDisplayMode=}^{__CFDictionary=}",
    ),
    "CGCursorIsVisible": (selAorI(b"i", b"I"),),
    "CGDisplayIsMain": (selAorI(b"iI", b"II"),),
    "CGSetLocalEventsFilterDuringSuppressionState": (b"iII",),
    "CGPDFDictionaryGetNumber": (
        b"B^{CGPDFDictionary=}^t^d",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGAssociateMouseAndMouseCursorPosition": (selAorI(b"ii", b"iI"),),
    "CGEventGetDoubleValueField": (b"d^{__CGEvent=}I",),
    "CGPDFDocumentAllowsPrinting": (b"B^{CGPDFDocument=}",),
    "CGContextSetBlendMode": (b"v^{CGContext=}i",),
    "CGFontGetGlyphAdvances": (
        b"B^{CGFont=}^SQ^i",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": 2, "type_modifier": "n"},
                3: {"c_array_length_in_arg": 2, "type_modifier": "o"},
            }
        },
    ),
    "CGContextReplacePathWithStrokedPath": (b"v^{CGContext=}",),
    "CGGetDisplayTransferByFormula": (
        b"iI^f^f^f^f^f^f^f^f^f",
        "",
        {
            "arguments": {
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
                5: {"type_modifier": "o"},
                6: {"type_modifier": "o"},
                7: {"type_modifier": "o"},
                8: {"type_modifier": "o"},
                9: {"type_modifier": "o"},
            }
        },
    ),
    "CGDisplayIsInHWMirrorSet": (selAorI(b"iI", b"II"),),
    "CGDisplayCapture": (b"iI",),
    "CGColorSpaceGetName": (b"^{__CFString=}^{CGColorSpace=}",),
    "CGImageGetAlphaInfo": (b"I^{CGImage=}",),
    "CGSizeCreateDictionaryRepresentation": (
        b"^{__CFDictionary=}{CGSize=dd}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFStringGetLength": (b"Q^{CGPDFString=}",),
    "CGScreenRegisterMoveCallback": (
        b"i^?^v",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"{CGScreenUpdateMoveDelta=ii}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^{CGRect={CGPoint=dd}{CGSize=dd}}"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGColorConversionInfoGetTypeID": (b"Q",),
    "CGPDFDocumentRetain": (b"^{CGPDFDocument=}^{CGPDFDocument=}",),
    "CGContextClip": (b"v^{CGContext=}",),
    "CGPreflightPostEventAccess": (b"B",),
    "CGWaitForScreenUpdateRects": (
        b"iI^I^^{CGRect={CGPoint=dd}{CGSize=dd}}^Q^{CGScreenUpdateMoveDelta=ii}",
    ),
    "CGDisplayBitsPerSample": (b"QI",),
    "CGDisplayModeGetPixelHeight": (b"Q^{CGDisplayMode=}",),
    "CGContextDrawTiledImage": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGImage=}",
    ),
    "CGBitmapContextGetBitsPerComponent": (b"Q^{CGContext=}",),
    "CGContextAddPath": (b"v^{CGContext=}^{CGPath=}",),
    "CGConfigureDisplayOrigin": (b"i^{_CGDisplayConfigRef=}Iii",),
    "CGContextSetCharacterSpacing": (b"v^{CGContext=}d",),
    "CGDisplaySamplesPerPixel": (b"QI",),
    "CGOpenGLDisplayMaskToDisplayID": (b"II",),
    "CGPDFArrayGetNumber": (
        b"B^{CGPDFArray=}Q^d",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGDisplaySetDisplayMode": (b"iI^{CGDisplayMode=}^{__CFDictionary=}",),
    "CGRectIsNull": (b"B{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGDataConsumerRelease": (b"v^{CGDataConsumer=}",),
    "CGColorSpaceCreateWithICCProfile": (
        b"^{CGColorSpace=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayModeGetWidth": (b"Q^{CGDisplayMode=}",),
    "CGCaptureAllDisplays": (b"i",),
    "CGPDFScannerPopArray": (
        b"B^{CGPDFScanner=}^^{CGPDFArray=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGContextClipToRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGDisplayStreamUpdateCreateMergedUpdate": (
        b"^{CGDisplayStreamUpdate=}^{CGDisplayStreamUpdate=}^{CGDisplayStreamUpdate=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayHideCursor": (b"iI",),
    "CGPDFDocumentGetPage": (b"^{CGPDFPage=}^{CGPDFDocument=}Q",),
    "CGSessionCopyCurrentDictionary": (
        b"^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGFontGetFontBBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGFont=}",),
    "CGImageGetBitsPerComponent": (b"Q^{CGImage=}",),
    "CGFontCopyTableTags": (
        b"^{__CFArray=}^{CGFont=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGWaitForScreenRefreshRects": (b"i^^{CGRect={CGPoint=dd}{CGSize=dd}}^I",),
    "CGDisplayStreamStop": (b"i^{CGDisplayStream=}",),
    "CGShadingRetain": (b"^{CGShading=}^{CGShading=}",),
    "CGBitmapContextGetColorSpace": (b"^{CGColorSpace=}^{CGContext=}",),
    "CGRequestPostEventAccess": (b"B",),
    "CGContextShowTextAtPoint": (
        b"v^{CGContext=}dd^tQ",
        "",
        {"arguments": {3: {"c_array_length_in_arg": 4, "type_modifier": "n"}}},
    ),
    "CGBitmapContextCreateImage": (
        b"^{CGImage=}^{CGContext=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextTranslateCTM": (b"v^{CGContext=}dd",),
    "CGDisplayModelNumber": (b"II",),
    "CGPDFContextCreateWithURL": (
        b"^{CGContext=}^{__CFURL=}^{CGRect={CGPoint=dd}{CGSize=dd}}^{__CFDictionary=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGColorSpaceCopyICCProfile": (
        b"^{__CFData=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorSpaceCopyBaseColorSpace": (
        b"^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetRGBStrokeColor": (b"v^{CGContext=}dddd",),
    "CGLayerRelease": (b"v^{CGLayer=}",),
    "CGBitmapContextCreateWithData": (
        b"^{CGContext=}^vQQQQ^{CGColorSpace=}I^?^v",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"^v"}},
                    }
                }
            },
        },
    ),
    "CGDisplayModeRelease": (b"v^{CGDisplayMode=}",),
    "CGAffineTransformConcat": (
        b"{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}",
    ),
    "CGImageGetDataProvider": (b"^{CGDataProvider=}^{CGImage=}",),
    "CGContextConvertRectToDeviceSpace": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGImageCreateWithMaskingColors": (
        b"^{CGImage=}^{CGImage=}^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGPDFDictionaryGetInteger": (
        b"B^{CGPDFDictionary=}^t^q",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGColorGetAlpha": (b"d^{CGColor=}",),
    "CGContextSetAllowsFontSubpixelPositioning": (b"v^{CGContext=}B",),
    "CGPDFDocumentIsEncrypted": (b"B^{CGPDFDocument=}",),
    "CGDisplayBestModeForParameters": (
        selAorI(b"^{__CFDictionary=}IQQQ^i", b"^{__CFDictionary=}IQQQ^I"),
        "",
        {"arguments": {4: {"type_modifier": "o"}}},
    ),
    "CGColorConversionInfoCreateForToneMapping": (
        b"^{CGColorConversionInfo=}^{CGColorSpace=}f^{CGColorSpace=}fI^{__CFDictionary=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                6: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                }
            },
        },
    ),
    "CGShadingCreateAxial": (
        b"^{CGShading=}^{CGColorSpace=}{CGPoint=dd}{CGPoint=dd}^{CGFunction=}BB",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGEventCreateData": (
        b"^{__CFData=}^{__CFAllocator=}^{__CGEvent=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorConverterRetain": (b"^{CGColorConversionInfo=}^{CGColorConversionInfo=}",),
    "CGRectMakeWithDictionaryRepresentation": (
        b"B^{__CFDictionary=}^{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGFunctionCreate": (
        b"^{CGFunction=}^vQ^dQ^d^{CGFunctionCallbacks=I^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPaletteCreateFromPaletteBlendedWithColor": (
        b"^{_CGDirectPaletteRef=}^{_CGDirectPaletteRef=}f{CGDeviceColor=fff}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextGetTextMatrix": (b"{CGAffineTransform=dddddd}^{CGContext=}",),
    "CGDisplayModeGetIOFlags": (b"I^{CGDisplayMode=}",),
    "CGDisplayModeCopyPixelEncoding": (
        b"^{__CFString=}^{CGDisplayMode=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGGetDisplaysWithRect": (
        b"i{CGRect={CGPoint=dd}{CGSize=dd}}I^I^I",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": (1, 3), "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CGFontCopyFullName": (
        b"^{__CFString=}^{CGFont=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGGetDisplaysWithPoint": (
        b"i{CGPoint=dd}I^I^I",
        "",
        {
            "arguments": {
                2: {"c_array_length_in_arg": (1, 3), "type_modifier": "o"},
                3: {"type_modifier": "o"},
            }
        },
    ),
    "CGPDFStreamCopyData": (
        b"^{__CFData=}^{CGPDFStream=}^i",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CGPathCreateSeparateComponents": (
        b"^{__CFArray=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGImageGetHeight": (b"Q^{CGImage=}",),
    "CGDisplayBitsPerPixel": (b"QI",),
    "CGPDFDocumentGetTrimBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFDocument=}i",),
    "CGGetActiveDisplayList": (
        b"iI^I^I",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": (0, 2), "type_modifier": "o"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGContextGetClipBoundingBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGContext=}",),
    "CGContextRetain": (b"^{CGContext=}^{CGContext=}",),
    "CGDisplayRemoveReconfigurationCallback": (
        b"i^?^v",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"I"},
                            1: {"type": b"I"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGPDFDocumentGetRotationAngle": (b"i^{CGPDFDocument=}i",),
    "CGDisplayModeGetPixelWidth": (b"Q^{CGDisplayMode=}",),
    "CGContextClipToRects": (
        b"v^{CGContext=}^{CGRect={CGPoint=dd}{CGSize=dd}}Q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGEventCreateCopy": (
        b"^{__CGEvent=}^{__CGEvent=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGEventSourceSetLocalEventsSuppressionInterval": (b"v^{__CGEventSource=}d",),
    "CGPDFDocumentGetBleedBox": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFDocument=}i",
    ),
    "CGPathGetCurrentPoint": (b"{CGPoint=dd}^{CGPath=}",),
    "CGContextShowGlyphsAtPositions": (
        b"v^{CGContext=}^S^{CGPoint=dd}Q",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
            }
        },
    ),
    "CGEventCreateMouseEvent": (
        b"^{__CGEvent=}^{__CGEventSource=}I{CGPoint=dd}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetStrokeColorWithColor": (b"v^{CGContext=}^{CGColor=}",),
    "CGPaletteSetColorAtIndex": (b"v^{_CGDirectPaletteRef=}{CGDeviceColor=fff}I",),
    "CGContextSetCMYKFillColor": (b"v^{CGContext=}ddddd",),
    "CGImageCreateWithImageInRect": (
        b"^{CGImage=}^{CGImage=}{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextBeginPage": (
        b"v^{CGContext=}^{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGImageCreateCopyWithColorSpace": (
        b"^{CGImage=}^{CGImage=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextDrawPDFDocument": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFDocument=}i",
    ),
    "CGEventGetUnflippedLocation": (b"{CGPoint=dd}^{__CGEvent=}",),
    "CGFunctionGetTypeID": (b"Q",),
    "CGCaptureAllDisplaysWithOptions": (b"iI",),
    "CGContextSetShadowWithColor": (b"v^{CGContext=}{CGSize=dd}d^{CGColor=}",),
    "CGContextSetInterpolationQuality": (b"v^{CGContext=}i",),
    "CGPDFPageGetDictionary": (b"^{CGPDFDictionary=}^{CGPDFPage=}",),
    "CGContextDrawLayerAtPoint": (b"v^{CGContext=}{CGPoint=dd}^{CGLayer=}",),
    "CGRequestScreenCaptureAccess": (b"B",),
    "CGDisplayRestoreColorSyncSettings": (b"v",),
    "CGPathCreateWithEllipseInRect": (
        b"^{CGPath=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGAffineTransform=dddddd}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CGPDFPageGetPageNumber": (b"Q^{CGPDFPage=}",),
    "CGColorRelease": (b"v^{CGColor=}",),
    "CGContextSetEDRTargetHeadroom": (b"B^{CGContext=}f",),
    "CGColorSpaceGetColorTable": (
        b"v^{CGColorSpace=}^C",
        "",
        {"arguments": {1: {"c_array_of_variable_length": True, "type_modifier": "o"}}},
    ),
    "CGColorSpaceCopyICCData": (
        b"^{__CFData=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayIsAlwaysInMirrorSet": (selAorI(b"iI", b"II"),),
    "CGFontCopyPostScriptName": (
        b"^{__CFString=}^{CGFont=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGFontCreateWithDataProvider": (
        b"^{CGFont=}^{CGDataProvider=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGRectCreateDictionaryRepresentation": (
        b"^{__CFDictionary=}{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGRectInset": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}dd",
    ),
    "CGRectGetWidth": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGContextStrokeRectWithWidth": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}d",
    ),
    "CGEventGetTypeID": (b"Q",),
    "CGFontGetItalicAngle": (b"d^{CGFont=}",),
    "CGColorSpaceCopyPropertyList": (
        b"@^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGRectUnion": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGPathAddRects": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}^{CGRect={CGPoint=dd}{CGSize=dd}}Q",
        "",
        {
            "arguments": {
                1: {"type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
            }
        },
    ),
    "CGColorSpaceSupportsOutput": (b"B^{CGColorSpace=}",),
    "CGPDFContextSetDestinationForRect": (
        b"v^{CGContext=}^{__CFString=}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGEventTapCreateForPid": (
        b"^{__CFMachPort=}iIIQ^?^v",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"^{__CGEvent=}"},
                        "arguments": {
                            0: {"type": b"^{__CGEventTapProxy=}"},
                            1: {"type": b"I"},
                            2: {"type": b"^{__CGEvent=}"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CGPDFOperatorTableRetain": (b"^{CGPDFOperatorTable=}^{CGPDFOperatorTable=}",),
    "CGPDFScannerPopName": (
        b"B^{CGPDFScanner=}^^t",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGLayerGetContext": (b"^{CGContext=}^{CGLayer=}",),
    "CGImageGetBitsPerPixel": (b"Q^{CGImage=}",),
    "CGPathAddArc": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}dddddB",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGContextDrawLinearGradient": (
        b"v^{CGContext=}^{CGGradient=}{CGPoint=dd}{CGPoint=dd}I",
    ),
    "CGDataConsumerGetTypeID": (b"Q",),
    "CGEventGetLocation": (b"{CGPoint=dd}^{__CGEvent=}",),
    "CGDisplayModeIsUsableForDesktopGUI": (b"B^{CGDisplayMode=}",),
    "CGRectIsInfinite": (b"B{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGEventSetTimestamp": (b"v^{__CGEvent=}Q",),
    "CGPDFContextAddDocumentMetadata": (b"v^{CGContext=}^{__CFData=}",),
    "CGDisplayWaitForBeamPositionOutsideLines": (b"iIII",),
    "CGColorSpaceCreateWithICCData": (
        b"^{CGColorSpace=}@",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFContextBeginPage": (b"v^{CGContext=}^{__CFDictionary=}",),
    "CGPSConverterIsConverting": (b"B^{CGPSConverter=}",),
    "CGEventPostToPid": (b"vi^{__CGEvent=}",),
    "CGPathIsEmpty": (b"B^{CGPath=}",),
    "CGDisplayScreenSize": (b"{CGSize=dd}I",),
    "CGDisplayIsInMirrorSet": (selAorI(b"iI", b"II"),),
    "CGFontCopyGlyphNameForGlyph": (
        b"^{__CFString=}^{CGFont=}S",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGBitmapContextGetAlphaInfo": (b"I^{CGContext=}",),
    "CGColorSpaceCreateCopyWithStandardRange": (
        b"^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFScannerRelease": (b"v^{CGPDFScanner=}",),
    "CGContextCopyPath": (
        b"^{CGPath=}^{CGContext=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGShadingRelease": (b"v^{CGShading=}",),
    "CGDisplayFadeOperationInProgress": (selAorI(b"i", b"I"),),
    "CGPostMouseEvent": (
        selAorI(b"i{CGPoint=dd}iIi", b"i{CGPoint=dd}III"),
        "",
        {"c_array_length_in_arg": 2, "variadic": True},
    ),
    "CGPDFContentStreamGetStreams": (b"^{__CFArray=}^{CGPDFContentStream=}",),
    "CGContextSetMiterLimit": (b"v^{CGContext=}d",),
    "CGRequestListenEventAccess": (b"B",),
    "CGPDFOperatorTableSetCallback": (
        b"v^{CGPDFOperatorTable=}^t^?",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{CGPDFScanner=}"},
                            1: {"type": b"^v"},
                        },
                    },
                    "callable_retained": True,
                },
            }
        },
    ),
    "CGEventTapIsEnabled": (b"B^{__CFMachPort=}",),
    "CGContextDrawPath": (b"v^{CGContext=}i",),
    "CGWindowServerCFMachPort": (b"^{__CFMachPort=}",),
    "CGColorEqualToColor": (b"B^{CGColor=}^{CGColor=}",),
    "CGContextSetFontSize": (b"v^{CGContext=}d",),
    "CGEventGetIntegerValueField": (b"q^{__CGEvent=}I",),
    "CGContextShowText": (
        b"v^{CGContext=}^tQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGColorSpaceCreateWithName": (
        b"^{CGColorSpace=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGRectGetMaxX": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGImageCreateWithMask": (
        b"^{CGImage=}^{CGImage=}^{CGImage=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGRectGetMidY": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGImageCreateWithPNGDataProvider": (
        b"^{CGImage=}^{CGDataProvider=}^dBi",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGContextSetTextDrawingMode": (b"v^{CGContext=}i",),
    "CGContextGetUserSpaceToDeviceSpaceTransform": (
        b"{CGAffineTransform=dddddd}^{CGContext=}",
    ),
    "CGDataConsumerCreateWithCFData": (
        b"^{CGDataConsumer=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathCreateCopyByUnioningPath": (
        b"^{CGPath=}^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorSpaceCreateCalibratedRGB": (
        b"^{CGColorSpace=}^d^d^d^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
                1: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
                2: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
                3: {"c_array_of_fixed_length": 9, "type_modifier": "n"},
            },
        },
    ),
    "CGContextSetLineJoin": (b"v^{CGContext=}i",),
    "CGDataProviderCreateSequential": (
        b"^{CGDataProvider=}^v^{CGDataProviderSequentialCallbacks=I^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFArrayGetNull": (b"B^{CGPDFArray=}Q",),
    "CGPDFScannerStop": (b"v^{CGPDFScanner=}",),
    "CGEventSourceSecondsSinceLastEventType": (b"diI",),
    "CGContextSetRGBFillColor": (b"v^{CGContext=}dddd",),
    "CGPaletteCreateCopy": (
        b"^{_CGDirectPaletteRef=}^{_CGDirectPaletteRef=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGGetLastMouseDelta": (
        b"v^i^i",
        "",
        {"arguments": {0: {"type_modifier": "o"}, 1: {"type_modifier": "o"}}},
    ),
    "CGPDFArrayGetDictionary": (
        b"B^{CGPDFArray=}Q^^{CGPDFDictionary=}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGContextGetEDRTargetHeadroom": (b"f^{CGContext=}",),
    "CGPDFArrayGetBoolean": (
        b"B^{CGPDFArray=}Q^C",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CGPathCloseSubpath": (b"v^{CGPath=}",),
    "CGPDFContentStreamCreateWithStream": (
        b"^{CGPDFContentStream=}^{CGPDFStream=}^{CGPDFDictionary=}^{CGPDFContentStream=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplaySerialNumber": (b"II",),
    "CGGetOnlineDisplayList": (
        b"iI^I^I",
        "",
        {
            "arguments": {
                1: {"c_array_length_in_arg": (0, 2), "type_modifier": "o"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGCancelDisplayConfiguration": (b"i^{_CGDisplayConfigRef=}",),
    "CGEventSourceSetPixelsPerLine": (b"v^{__CGEventSource=}d",),
    "CGAffineTransformEqualToTransform": (
        b"B{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}",
    ),
    "CGContextSetShouldSubpixelPositionFonts": (b"v^{CGContext=}B",),
    "CGContextSaveGState": (b"v^{CGContext=}",),
    "CGColorSpaceCreateLinearized": (
        b"^{CGColorSpace=}^{CGColorSpace=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGReleaseAllDisplays": (b"i",),
    "CGDisplayRelease": (b"iI",),
    "CGContextConvertSizeToUserSpace": (b"{CGSize=dd}^{CGContext=}{CGSize=dd}",),
    "CGColorCreateCopyWithAlpha": (
        b"^{CGColor=}^{CGColor=}d",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFScannerPopNumber": (
        b"B^{CGPDFScanner=}^d",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGContextEOFillPath": (b"v^{CGContext=}",),
    "CGImageGetContentHeadroom": (b"f^{CGImage=}",),
    "CGPSConverterAbort": (b"B^{CGPSConverter=}",),
    "CGContextFillPath": (b"v^{CGContext=}",),
    "CGDisplayBaseAddress": (
        b"^vI",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CGContextFillRects": (
        b"v^{CGContext=}^{CGRect={CGPoint=dd}{CGSize=dd}}Q",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGContextStrokeEllipseInRect": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "CGEventGetFlags": (b"Q^{__CGEvent=}",),
    "CGPDFContextEndPage": (b"v^{CGContext=}",),
    "CGEventSourceButtonState": (b"BiI",),
    "CGContextConvertSizeToDeviceSpace": (b"{CGSize=dd}^{CGContext=}{CGSize=dd}",),
    "CGPDFDictionaryApplyBlock": (
        b"v^{CGPDFDictionary=}@?^v",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"c_array_delimited_by_null": True, "type": "n^t"},
                            2: {"type": "^{CGPDFObject=}"},
                            3: {"type": "^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGReleaseScreenRefreshRects": (b"v^{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGContextSetFlatness": (b"v^{CGContext=}d",),
    "CGColorSpaceUsesExtendedRange": (b"B^{CGColorSpace=}",),
    "CGContextDrawShading": (b"v^{CGContext=}^{CGShading=}",),
    "CGImageCreateWithContentHeadroom": (
        b"^{CGImage=}fQQQQQ^{CGColorSpace=}I^{CGDataProvider=}^dBi",
        "",
        {
            "comment": "API documentation is unclear about this parameter",
            "retval": {"already_cfretained": True},
            "arguments": {
                9: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGWindowLevelForKey": (b"ii",),
    "CGFontCopyVariationAxes": (
        b"^{__CFArray=}^{CGFont=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextDrawImage": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGImage=}",
    ),
    "CGPDFDocumentCreateWithProvider": (
        b"^{CGPDFDocument=}^{CGDataProvider=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFDocumentGetArtBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFDocument=}i",),
    "CGContextBeginPath": (b"v^{CGContext=}",),
    "CGEventCreateScrollWheelEvent2": (
        b"^{__CGEvent=}^{__CGEventSource=}IIiii",
        "",
        {
            "retval": {"already_cfretained": True},
            "c_array_length_in_arg": 2,
            "variadic": True,
        },
    ),
    "CGColorConversionInfoCreateWithOptions": (
        b"^{CGColorConversionInfo=}^{CGColorSpace=}^{CGColorSpace=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathIsRect": (
        b"B^{CGPath=}^{CGRect={CGPoint=dd}{CGSize=dd}}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGPathCreateCopyByDashingPath": (
        b"^{CGPath=}^{CGPath=}^{CGAffineTransform=dddddd}d^dQ",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"type_modifier": "n"},
                3: {"c_array_length_in_arg": 4, "type_modifier": "n"},
            },
        },
    ),
    "CGDisplayIsAsleep": (selAorI(b"iI", b"II"),),
    "CGPSConverterConvert": (
        b"B^{CGPSConverter=}^{CGDataProvider=}^{CGDataConsumer=}^{__CFDictionary=}",
    ),
    "CGEventCreateSourceFromEvent": (
        b"^{__CGEventSource=}^{__CGEvent=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayAddressForPosition": (
        b"^vIii",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CGContextConvertPointToDeviceSpace": (b"{CGPoint=dd}^{CGContext=}{CGPoint=dd}",),
    "CGDisplayBounds": (b"{CGRect={CGPoint=dd}{CGSize=dd}}I",),
    "CGEventTapEnable": (b"v^{__CFMachPort=}B",),
    "CGColorGetConstantColor": (b"^{CGColor=}^{__CFString=}",),
    "CGDisplayUnitNumber": (b"II",),
    "CGContextClosePath": (b"v^{CGContext=}",),
    "CGFontGetStemV": (b"d^{CGFont=}",),
    "CGDisplayCopyDisplayMode": (
        b"^{CGDisplayMode=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFDocumentAllowsCopying": (
        b"B^{CGPDFDocument=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayCreateImage": (
        b"^{CGImage=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathCreateCopyBySubtractingPath": (
        b"^{CGPath=}^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGDisplayRotation": (b"dI",),
    "CGDisplayBestModeForParametersAndRefreshRateWithProperty": (
        b"^{__CFDictionary=}IQQQd^{__CFString=}^I",
        "",
        {"arguments": {6: {"type_modifier": "o"}}},
    ),
    "CGDisplayIDToOpenGLDisplayMask": (b"II",),
    "CGColorCreate": (
        b"^{CGColor=}^{CGColorSpace=}^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGEventTapPostEvent": (b"v^{__CGEventTapProxy=}^{__CGEvent=}",),
    "CGImageGetByteOrderInfo": (b"I^{CGImage=}",),
    "CGDisplayBeamPosition": (b"II",),
    "CGPDFDocumentGetTypeID": (b"Q",),
    "CGShadingGetTypeID": (b"Q",),
    "CGEventSourceKeyState": (b"BiS",),
    "CGContextBeginTransparencyLayerWithRect": (
        b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{__CFDictionary=}",
    ),
    "CGContextDrawImageApplyingToneMapping": (
        b"B^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}^{CGImage=}I^{__CFDictionary=}",
    ),
    "CGPointCreateDictionaryRepresentation": (
        b"^{__CFDictionary=}{CGPoint=dd}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorCreateWithPattern": (
        b"^{CGColor=}^{CGColorSpace=}^{CGPattern=}^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGContextDrawConicGradient": (b"v^{CGContext=}^{CGGradient=}{CGPoint=dd}d",),
    "CGDisplayPixelsWide": (b"QI",),
    "CGEventSourceFlagsState": (b"Qi",),
    "CGLayerRetain": (b"^{CGLayer=}^{CGLayer=}",),
    "CGContextFillRect": (b"v^{CGContext=}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGAffineTransformInvert": (
        b"{CGAffineTransform=dddddd}{CGAffineTransform=dddddd}",
    ),
    "CGPaletteGetColorAtIndex": (b"{CGDeviceColor=fff}^{_CGDirectPaletteRef=}I",),
    "CGEventSetFlags": (b"v^{__CGEvent=}Q",),
    "CGPDFDocumentGetCropBox": (b"{CGRect={CGPoint=dd}{CGSize=dd}}^{CGPDFDocument=}i",),
    "CGEventCreateScrollWheelEvent": (
        b"^{__CGEvent=}^{__CGEventSource=}IIi",
        "",
        {
            "retval": {"already_cfretained": True},
            "c_array_length_in_arg": 2,
            "variadic": True,
        },
    ),
    "CGPatternGetTypeID": (b"Q",),
    "CGImageGetBytesPerRow": (b"Q^{CGImage=}",),
    "CGPathRelease": (b"v^{CGPath=}",),
    "CGDisplayModeGetTypeID": (b"Q",),
    "CGEventGetType": (b"I^{__CGEvent=}",),
    "CGContextSetAllowsFontSubpixelQuantization": (b"v^{CGContext=}B",),
    "CGPathMoveToPoint": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}dd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGFontRelease": (b"v^{CGFont=}",),
    "CGPSConverterGetTypeID": (b"Q",),
    "CGPDFDocumentRelease": (b"v^{CGPDFDocument=}",),
    "CGContextSetLineWidth": (b"v^{CGContext=}d",),
    "CGDisplayStreamCreateWithDispatchQueue": (
        b"^{CGDisplayStream=}IQQi^{__CFDictionary=}@@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": "^v"},
                            1: {"type": "I"},
                            2: {"type": "Q"},
                            3: {"type": "@"},
                            4: {"type": "@"},
                        },
                    }
                }
            },
        },
    ),
    "CGColorSpaceCreateWithPropertyList": (
        b"^{CGColorSpace=}@",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextGetPathCurrentPoint": (b"{CGPoint=dd}^{CGContext=}",),
    "CGContextSetPatternPhase": (b"v^{CGContext=}{CGSize=dd}",),
    "CGFontGetAscent": (b"i^{CGFont=}",),
    "CGPDFDictionaryGetDictionary": (
        b"B^{CGPDFDictionary=}^t^^{CGPDFDictionary=}",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGContextShowGlyphs": (
        b"v^{CGContext=}^SQ",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CGFontGetLeading": (b"i^{CGFont=}",),
    "CGGradientCreateWithColorComponents": (
        b"^{CGGradient=}^{CGColorSpace=}^d^dQ",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_length_in_arg": 3, "type_modifier": "n"},
                2: {"c_array_length_in_arg": 3, "type_modifier": "n"},
            },
        },
    ),
    "CGDisplayGetDrawingContext": (b"^{CGContext=}I",),
    "CGContextMoveToPoint": (b"v^{CGContext=}dd",),
    "CGDisplayStreamUpdateGetTypeID": (b"Q",),
    "CGImageContainsImageSpecificToneMappingMetadata": (b"B^{CGImage=}",),
    "CGRectGetMinY": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGRectGetMinX": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "CGContextSetFont": (b"v^{CGContext=}^{CGFont=}",),
    "CGDisplayShowCursor": (b"iI",),
    "CGContextRestoreGState": (b"v^{CGContext=}",),
    "CGPathRetain": (b"^{CGPath=}^{CGPath=}",),
    "CGPDFDocumentGetNumberOfPages": (b"Q^{CGPDFDocument=}",),
    "CGAffineTransformMakeScale": (b"{CGAffineTransform=dddddd}dd",),
    "CGPathAddCurveToPoint": (
        b"v^{CGPath=}^{CGAffineTransform=dddddd}dddddd",
        "",
        {"arguments": {1: {"type_modifier": "n"}}},
    ),
    "CGImageCreate": (
        b"^{CGImage=}QQQQQ^{CGColorSpace=}I^{CGDataProvider=}^dBi",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                8: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGContextSetShouldSmoothFonts": (b"v^{CGContext=}B",),
    "CGPathCreateCopyByIntersectingPath": (
        b"^{CGPath=}^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFDictionaryGetBoolean": (
        b"B^{CGPDFDictionary=}^t^C",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "CGPDFStringGetBytePtr": (
        b"^C^{CGPDFString=}",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CGScreenUnregisterMoveCallback": (
        b"v^?^v",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"{CGScreenUpdateMoveDelta=ii}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^{CGRect={CGPoint=dd}{CGSize=dd}}"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CGColorGetColorSpace": (b"^{CGColorSpace=}^{CGColor=}",),
    "CGPathCreateCopyOfLineBySubtractingPath": (
        b"^{CGPath=}^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGColorConverterRelease": (b"v^{CGColorConversionInfo=}",),
    "CGColorSpaceUsesITUR_2100TF": (b"B^{CGColorSpace=}",),
    "CGDisplayAvailableModes": (b"^{__CFArray=}I",),
    "CGColorCreateGenericGrayGamma2_2": (
        b"^{CGColor=}dd",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGContextSetTextMatrix": (b"v^{CGContext=}{CGAffineTransform=dddddd}",),
    "CGPostKeyboardEvent": (selAorI(b"iSSi", b"iSSI"),),
    "CGPathCreateCopyBySymmetricDifferenceOfPath": (
        b"^{CGPath=}^{CGPath=}^{CGPath=}B",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPDFScannerPopStream": (
        b"B^{CGPDFScanner=}^^{CGPDFStream=}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGPathCreateMutableCopy": (
        b"^{CGPath=}^{CGPath=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CGPathGetTypeID": (b"Q",),
    "CGColorSpaceCreateIndexed": (
        b"^{CGColorSpace=}^{CGColorSpace=}Q^C",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CGColorSpaceCreateLab": (
        b"^{CGColorSpace=}^d^d^d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                0: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
                1: {"c_array_of_fixed_length": 3, "type_modifier": "n"},
                2: {"c_array_of_fixed_length": 4, "type_modifier": "n"},
            },
        },
    ),
    "CGDisplayIOServicePort": (b"II",),
    "CGContextSetFillPattern": (
        b"v^{CGContext=}^{CGPattern=}^d",
        "",
        {"arguments": {2: {"c_array_of_variable_length": True, "type_modifier": "n"}}},
    ),
    "CGPreflightScreenCaptureAccess": (b"B",),
    "CGSizeMakeWithDictionaryRepresentation": (
        b"B^{__CFDictionary=}^{CGSize=dd}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CGPDFDocumentUnlockWithPassword": (
        b"B^{CGPDFDocument=}^t",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "CGConvertColorDataWithFormat": (
        b"BQQ^v{CGColorDataFormat=I@IQQi^d}^v{CGColorDataFormat=I@IQQi^d}^{__CFDictionary=}",
        "",
        {
            "arguments": {
                2: {"c_array_of_variable_length": True, "type_modifier": "o"},
                4: {"c_array_of_variable_length": True, "type_modifier": "n"},
            }
        },
    ),
    "CGContextSetFillColorWithColor": (b"v^{CGContext=}^{CGColor=}",),
    "CGPDFDictionaryGetName": (
        b"B^{CGPDFDictionary=}^t^^t",
        "",
        {
            "arguments": {
                1: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                2: {"type_modifier": "o"},
            }
        },
    ),
}
aliases = {
    "CGSizeApplyAffineTransform": "__CGSizeApplyAffineTransform",
    "CG_NULLABLE_ARRAY": "__nullable",
    "CGSizeEqualToSize": "__CGSizeEqualToSize",
    "CGFLOAT_TYPE": "double",
    "CGPointApplyAffineTransform": "__CGPointApplyAffineTransform",
    "kCGWindowIDCFNumberType": "kCFNumberSInt32Type",
    "CGColorConverterRef": "CGColorConversionInfoRef",
    "kCGBitmapByteOrder16Host": "kCGBitmapByteOrder16Little",
    "CGPointEqualToPoint": "__CGPointEqualToPoint",
    "kCGEventSupressionStateRemoteMouseDrag": "kCGEventSuppressionStateRemoteMouseDrag",
    "kCGEventSupressionStateSupressionInterval": "kCGEventSuppressionStateSuppressionInterval",
    "CG_SKYLIGHT_EXTERN_64": "CG_EXTERN",
    "CGEventSupressionState": "CGEventSuppressionState",
    "kCGBitmapByteOrder32Big": "kCGImageByteOrder32Big",
    "CG_PRIVATE_EXTERN": "CG_LOCAL",
    "CG_AVAILABLE_STARTING": "__OSX_AVAILABLE_STARTING",
    "CG_LOCAL": "__private_extern__",
    "cg_nullable": "__nullable",
    "kCGGlyphMax": "kCGFontIndexMax",
    "CG_EXTERN_64": "CG_EXTERN",
    "CG_NONNULL_ARRAY": "__nonnull",
    "kCGWindowBackingCFNumberType": "kCFNumberSInt32Type",
    "CGFLOAT_MIN": "DBL_MIN",
    "CG_LOCAL_64": "CG_LOCAL",
    "kCGBitmapByteOrderMask": "kCGImageByteOrderMask",
    "CGEventNoErr": "kCGErrorSuccess",
    "CGAffineTransformMake": "__CGAffineTransformMake",
    "kCGBitmapByteOrder16Little": "kCGImageByteOrder16Little",
    "CGFLOAT_MAX": "DBL_MAX",
    "kCGBitmapByteOrderDefault": "kCGImageByteOrderDefault",
    "CGFLOAT_EPSILON": "DBL_EPSILON",
    "kCGWindowSharingCFNumberType": "kCFNumberSInt32Type",
    "kCGBitmapByteOrder16Big": "kCGImageByteOrder16Big",
    "CGDisplayNoErr": "kCGErrorSuccess",
    "CG_OBSOLETE": "__CG_DEPRECATED",
    "kCGNumberOfEventSupressionStates": "kCGNumberOfEventSuppressionStates",
    "CG_AVAILABLE_BUT_DEPRECATED": "__OSX_AVAILABLE_BUT_DEPRECATED",
    "CGSetLocalEventsFilterDuringSupressionState": "CGSetLocalEventsFilterDuringSuppressionState",
    "kCGBitmapByteOrder32Little": "kCGImageByteOrder32Little",
    "kCGBitmapByteOrder32Host": "kCGBitmapByteOrder32Little",
}
cftypes = [
    ("CGEventRef", b"^{__CGEvent=}", "CGEventGetTypeID", None),
    ("CGEventSourceRef", b"^{__CGEventSource=}", "CGEventSourceGetTypeID", None),
    ("IOSurfaceRef", b"^{__IOSurface=}", None, None),
    ("CGColorRef", b"^{CGColor=}", "CGColorGetTypeID", None),
    ("CGFontRef", b"^{CGFont=}", "CGFontGetTypeID", None),
    (
        "CGColorConversionInfoRef",
        b"^{CGColorConversionInfo=}",
        "CGColorConversionInfoGetTypeID",
        None,
    ),
    ("CGDataConsumerRef", b"^{CGDataConsumer=}", "CGDataConsumerGetTypeID", None),
    ("CGPathRef", b"^{CGPath=}", "CGPathGetTypeID", None),
    ("CGDisplayModeRef", b"^{CGDisplayMode}", "CGDisplayModeGetTypeID", None),
    ("CGGradientRef", b"^{CGGradient=}", "CGGradientGetTypeID", None),
    ("CGDirectDisplay", b"^{CGDirectDisplay=}", "CGDirectDisplayGetTypeID", None),
    ("CGColorSpaceRef", b"^{CGColorSpace=}", "CGColorSpaceGetTypeID", None),
    ("CGPDFDocumentRef", b"^{CGPDFDocument=}", "CGPDFDocumentGetTypeID", None),
    ("CGFunctionRef", b"^{CGFunction=}", "CGFunctionGetTypeID", None),
    ("CGImageRef", b"^{CGImage=}", "CGImageGetTypeID", None),
    ("CGDisplayStreamRef", b"^{CGDisplayStream=}", "CGDisplayStreamGetTypeID", None),
    ("CGPDFPageRef", b"^{CGPDFPage=}", "CGPDFPageGetTypeID", None),
    ("CGLayerRef", b"^{CGLayer=}", "CGLayerGetTypeID", None),
    ("CGPatternRef", b"^{CGPattern=}", "CGPatternGetTypeID", None),
    ("CGPSConverterRef", b"^{CGPSConverter=}", "CGPSConverterGetTypeID", None),
    ("CGContextRef", b"^{CGContext=}", "CGContextGetTypeID", None),
    ("CGShadingRef", b"^{CGShading=}", "CGShadingGetTypeID", None),
    ("CGDataProviderRef", b"^{CGDataProvider=}", "CGDataProviderGetTypeID", None),
    (
        "CGDisplayStreamUpdateRef",
        b"^{CGDisplayStreamUpdate=}",
        "CGDisplayStreamUpdateGetTypeID",
        None,
    ),
]
misc.update(
    {
        "CGPDFContentStreamRef": objc.createOpaquePointerType(
            "CGPDFContentStreamRef", b"^{CGPDFContentStream=}"
        ),
        "CGPDFScannerRef": objc.createOpaquePointerType(
            "CGPDFScannerRef", b"^{CGPDFScanner=}"
        ),
        "CGDirectPaletteRef": objc.createOpaquePointerType(
            "CGDirectPaletteRef", b"^{_CGDirectPaletteRef=}"
        ),
        "CGPDFStringRef": objc.createOpaquePointerType(
            "CGPDFStringRef", b"^{CGPDFString=}"
        ),
        "CGPDFObject": objc.createOpaquePointerType("CGPDFObject", b"^{CGPDFObject=}"),
        "CGPDFOperatorTableRef": objc.createOpaquePointerType(
            "CGPDFOperatorTableRef", b"^{CGPDFOperatorTable}"
        ),
        "CGDisplayConfigRef": objc.createOpaquePointerType(
            "CGDisplayConfigRef", b"^{_CGDisplayConfigRef=}"
        ),
        "CGPDFDictionaryRef": objc.createOpaquePointerType(
            "CGPDFDictionaryRef", b"^{CGPDFDictionary=}"
        ),
        "CGPDFStreamRef": objc.createOpaquePointerType(
            "CGPDFStreamRef", b"^{CGPDFStream=}"
        ),
        "CGEventTapProxy": objc.createOpaquePointerType(
            "CGEventTapProxy", b"^{__CGEventTapProxy=}"
        ),
        "CGPDFArrayRef": objc.createOpaquePointerType(
            "CGPDFArrayRef", b"^{CGPDFArray=}"
        ),
    }
)
expressions = {
    "kCGOverlayWindowLevel": "CGWindowLevelForKey(kCGOverlayWindowLevelKey)",
    "kCGMainMenuWindowLevel": "CGWindowLevelForKey(kCGMainMenuWindowLevelKey)",
    "kCGBaseWindowLevel": "CGWindowLevelForKey(kCGBaseWindowLevelKey)",
    "kCGStatusWindowLevel": "CGWindowLevelForKey(kCGStatusWindowLevelKey)",
    "kCGHelpWindowLevel": "CGWindowLevelForKey(kCGHelpWindowLevelKey)",
    "kCGNormalWindowLevel": "CGWindowLevelForKey(kCGNormalWindowLevelKey)",
    "kCGScreenSaverWindowLevel": "CGWindowLevelForKey(kCGScreenSaverWindowLevelKey)",
    "kCGMaximumWindowLevel": "CGWindowLevelForKey(kCGMaximumWindowLevelKey)",
    "kCGUtilityWindowLevel": "CGWindowLevelForKey(kCGUtilityWindowLevelKey)",
    "kCGCursorWindowLevel": "CGWindowLevelForKey(kCGCursorWindowLevelKey)",
    "kCGAssistiveTechHighWindowLevel": "CGWindowLevelForKey(kCGAssistiveTechHighWindowLevelKey)",
    "kCGModalPanelWindowLevel": "CGWindowLevelForKey(kCGModalPanelWindowLevelKey)",
    "kCGEventFilterMaskPermitAllEvents": "(kCGEventFilterMaskPermitLocalMouseEvents | kCGEventFilterMaskPermitLocalKeyboardEvents | kCGEventFilterMaskPermitSystemDefinedEvents)",
    "kCGMinimumWindowLevel": "CGWindowLevelForKey(kCGMinimumWindowLevelKey)",
    "kCGPopUpMenuWindowLevel": "CGWindowLevelForKey(kCGPopUpMenuWindowLevelKey)",
    "kCGDesktopWindowLevel": "CGWindowLevelForKey(kCGDesktopWindowLevelKey)",
    "kCGFloatingWindowLevel": "CGWindowLevelForKey(kCGFloatingWindowLevelKey)",
    "kCGDraggingWindowLevel": "CGWindowLevelForKey(kCGDraggingWindowLevelKey)",
    "kCGDockWindowLevel": "CGWindowLevelForKey(kCGDockWindowLevelKey)",
    "kCGDesktopIconWindowLevel": "CGWindowLevelForKey(kCGDesktopIconWindowLevelKey)",
    "kCGBackstopMenuLevel": "CGWindowLevelForKey(kCGBackstopMenuLevelKey)",
    "kCGTornOffMenuWindowLevel": "CGWindowLevelForKey(kCGTornOffMenuWindowLevelKey)",
}

# END OF FILE
