# This file is generated by objective.metadata
#
# Last update: Fri Nov 15 12:06:08 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "CATransform3D": objc.createStructType(
            "Quartz.QuartzCore.CATransform3D",
            b"{CATransform3D=dddddddddddddddd}",
            [
                "m11",
                "m12",
                "m13",
                "m14",
                "m21",
                "m22",
                "m23",
                "m24",
                "m31",
                "m32",
                "m33",
                "m34",
                "m41",
                "m42",
                "m43",
                "m44",
            ],
        ),
        "CAFrameRateRange": objc.createStructType(
            "Quartz.QuartzCore.CAFrameRateRange",
            b"{CAFrameRateRange=fff}",
            ["minimum", "maximum", "preferred"],
        ),
    }
)
constants = """$CAFrameRateRangeDefault@{CAFrameRateRange=fff}$CAToneMapModeAutomatic$CAToneMapModeIfSupported$CAToneMapModeNever$CATransform3DIdentity@{CATransform3D=dddddddddddddddd}$CIDetectorAccuracy$CIDetectorAccuracyHigh$CIDetectorAccuracyLow$CIDetectorAspectRatio$CIDetectorEyeBlink$CIDetectorFocalLength$CIDetectorImageOrientation$CIDetectorMaxFeatureCount$CIDetectorMinFeatureSize$CIDetectorNumberOfAngles$CIDetectorReturnSubFeatures$CIDetectorSmile$CIDetectorTracking$CIDetectorTypeFace$CIDetectorTypeQRCode$CIDetectorTypeRectangle$CIDetectorTypeText$CIFeatureTypeFace$CIFeatureTypeQRCode$CIFeatureTypeRectangle$CIFeatureTypeText$CIRAWDecoderVersion6$CIRAWDecoderVersion6DNG$CIRAWDecoderVersion7$CIRAWDecoderVersion7DNG$CIRAWDecoderVersion8$CIRAWDecoderVersion8DNG$CIRAWDecoderVersionNone$kCAAlignmentCenter$kCAAlignmentJustified$kCAAlignmentLeft$kCAAlignmentNatural$kCAAlignmentRight$kCAAnimationCubic$kCAAnimationCubicPaced$kCAAnimationDiscrete$kCAAnimationLinear$kCAAnimationPaced$kCAAnimationRotateAuto$kCAAnimationRotateAutoReverse$kCAContentsFormatGray8Uint$kCAContentsFormatRGBA16Float$kCAContentsFormatRGBA8Uint$kCACornerCurveCircular$kCACornerCurveContinuous$kCAEmitterBehaviorAlignToMotion$kCAEmitterBehaviorAttractor$kCAEmitterBehaviorColorOverLife$kCAEmitterBehaviorDrag$kCAEmitterBehaviorLight$kCAEmitterBehaviorSimpleAttractor$kCAEmitterBehaviorValueOverLife$kCAEmitterBehaviorWave$kCAEmitterLayerAdditive$kCAEmitterLayerBackToFront$kCAEmitterLayerCircle$kCAEmitterLayerCuboid$kCAEmitterLayerLine$kCAEmitterLayerOldestFirst$kCAEmitterLayerOldestLast$kCAEmitterLayerOutline$kCAEmitterLayerPoint$kCAEmitterLayerPoints$kCAEmitterLayerRectangle$kCAEmitterLayerSphere$kCAEmitterLayerSurface$kCAEmitterLayerUnordered$kCAEmitterLayerVolume$kCAFillModeBackwards$kCAFillModeBoth$kCAFillModeForwards$kCAFillModeFrozen$kCAFillModeRemoved$kCAFillRuleEvenOdd$kCAFillRuleNonZero$kCAFilterLinear$kCAFilterNearest$kCAFilterTrilinear$kCAGradientLayerAxial$kCAGradientLayerConic$kCAGradientLayerRadial$kCAGravityBottom$kCAGravityBottomLeft$kCAGravityBottomRight$kCAGravityCenter$kCAGravityLeft$kCAGravityResize$kCAGravityResizeAspect$kCAGravityResizeAspectFill$kCAGravityRight$kCAGravityTop$kCAGravityTopLeft$kCAGravityTopRight$kCALineCapButt$kCALineCapRound$kCALineCapSquare$kCALineJoinBevel$kCALineJoinMiter$kCALineJoinRound$kCAMediaTimingFunctionDefault$kCAMediaTimingFunctionEaseIn$kCAMediaTimingFunctionEaseInEaseOut$kCAMediaTimingFunctionEaseOut$kCAMediaTimingFunctionLinear$kCAOnOrderIn$kCAOnOrderOut$kCARendererColorSpace$kCARendererMetalCommandQueue$kCAScrollBoth$kCAScrollHorizontally$kCAScrollNone$kCAScrollVertically$kCATransactionAnimationDuration$kCATransactionAnimationTimingFunction$kCATransactionCompletionBlock$kCATransactionDisableActions$kCATransition$kCATransitionFade$kCATransitionFromBottom$kCATransitionFromLeft$kCATransitionFromRight$kCATransitionFromTop$kCATransitionMoveIn$kCATransitionPush$kCATransitionReveal$kCATruncationEnd$kCATruncationMiddle$kCATruncationNone$kCATruncationStart$kCAValueFunctionRotateX$kCAValueFunctionRotateY$kCAValueFunctionRotateZ$kCAValueFunctionScale$kCAValueFunctionScaleX$kCAValueFunctionScaleY$kCAValueFunctionScaleZ$kCAValueFunctionTranslate$kCAValueFunctionTranslateX$kCAValueFunctionTranslateY$kCAValueFunctionTranslateZ$kCIActiveKeys$kCIApplyOptionColorSpace$kCIApplyOptionDefinition$kCIApplyOptionExtent$kCIApplyOptionUserInfo$kCIAttributeClass$kCIAttributeDefault$kCIAttributeDescription$kCIAttributeDisplayName$kCIAttributeFilterAvailable_Mac$kCIAttributeFilterAvailable_iOS$kCIAttributeFilterCategories$kCIAttributeFilterDisplayName$kCIAttributeFilterName$kCIAttributeIdentity$kCIAttributeMax$kCIAttributeMin$kCIAttributeName$kCIAttributeReferenceDocumentation$kCIAttributeSliderMax$kCIAttributeSliderMin$kCIAttributeType$kCIAttributeTypeAngle$kCIAttributeTypeBoolean$kCIAttributeTypeColor$kCIAttributeTypeCount$kCIAttributeTypeDistance$kCIAttributeTypeGradient$kCIAttributeTypeImage$kCIAttributeTypeInteger$kCIAttributeTypeOffset$kCIAttributeTypeOpaqueColor$kCIAttributeTypePosition$kCIAttributeTypePosition3$kCIAttributeTypeRectangle$kCIAttributeTypeScalar$kCIAttributeTypeTime$kCIAttributeTypeTransform$kCICategoryBlur$kCICategoryBuiltIn$kCICategoryColorAdjustment$kCICategoryColorEffect$kCICategoryCompositeOperation$kCICategoryDistortionEffect$kCICategoryFilterGenerator$kCICategoryGenerator$kCICategoryGeometryAdjustment$kCICategoryGradient$kCICategoryHalftoneEffect$kCICategoryHighDynamicRange$kCICategoryInterlaced$kCICategoryNonSquarePixels$kCICategoryReduction$kCICategorySharpen$kCICategoryStillImage$kCICategoryStylize$kCICategoryTileEffect$kCICategoryTransition$kCICategoryVideo$kCIContextAllowLowPower$kCIContextCacheIntermediates$kCIContextHighQualityDownsample$kCIContextMemoryLimit$kCIContextName$kCIContextOutputColorSpace$kCIContextOutputPremultiplied$kCIContextPriorityRequestLow$kCIContextUseSoftwareRenderer$kCIContextWorkingColorSpace$kCIContextWorkingFormat$kCIFilterGeneratorExportedKey$kCIFilterGeneratorExportedKeyName$kCIFilterGeneratorExportedKeyTargetObject$kCIFormatA16@i$kCIFormatA8@i$kCIFormatABGR8@i$kCIFormatARGB8@i$kCIFormatAf@i$kCIFormatAh@i$kCIFormatBGRA8@i$kCIFormatL16@i$kCIFormatL8@i$kCIFormatLA16@i$kCIFormatLA8@i$kCIFormatLAf@i$kCIFormatLAh@i$kCIFormatLf@i$kCIFormatLh@i$kCIFormatR16@i$kCIFormatR8@i$kCIFormatRG16@i$kCIFormatRG8@i$kCIFormatRGB10@i$kCIFormatRGBA16@i$kCIFormatRGBA8@i$kCIFormatRGBAf@i$kCIFormatRGBAh@i$kCIFormatRGBX16@i$kCIFormatRGBXf@i$kCIFormatRGBXh@i$kCIFormatRGf@i$kCIFormatRGh@i$kCIFormatRf@i$kCIFormatRh@i$kCIImageApplyOrientationProperty$kCIImageAutoAdjustCrop$kCIImageAutoAdjustEnhance$kCIImageAutoAdjustFeatures$kCIImageAutoAdjustLevel$kCIImageAutoAdjustRedEye$kCIImageAuxiliaryDepth$kCIImageAuxiliaryDisparity$kCIImageAuxiliaryHDRGainMap$kCIImageAuxiliaryPortraitEffectsMatte$kCIImageAuxiliarySemanticSegmentationHairMatte$kCIImageAuxiliarySemanticSegmentationSkinMatte$kCIImageAuxiliarySemanticSegmentationTeethMatte$kCIImageCacheImmediately$kCIImageColorSpace$kCIImageContentHeadroom$kCIImageExpandToHDR$kCIImageNearestSampling$kCIImageProperties$kCIImageProviderTileSize$kCIImageProviderUserInfo$kCIImageRepresentationAVDepthData$kCIImageRepresentationAVPortraitEffectsMatte$kCIImageRepresentationAVSemanticSegmentationMattes$kCIImageRepresentationDepthImage$kCIImageRepresentationDisparityImage$kCIImageRepresentationHDRGainMapImage$kCIImageRepresentationHDRImage$kCIImageRepresentationPortraitEffectsMatteImage$kCIImageRepresentationSemanticSegmentationHairMatteImage$kCIImageRepresentationSemanticSegmentationSkinMatteImage$kCIImageRepresentationSemanticSegmentationTeethMatteImage$kCIImageTextureFormat$kCIImageTextureTarget$kCIInputAllowDraftModeKey$kCIInputAmountKey$kCIInputAngleKey$kCIInputAspectRatioKey$kCIInputBackgroundImageKey$kCIInputBaselineExposureKey$kCIInputBiasKey$kCIInputBoostKey$kCIInputBoostShadowAmountKey$kCIInputBrightnessKey$kCIInputCenterKey$kCIInputColorKey$kCIInputColorNoiseReductionAmountKey$kCIInputContrastKey$kCIInputDecoderVersionKey$kCIInputDepthImageKey$kCIInputDisableGamutMapKey$kCIInputDisparityImageKey$kCIInputEVKey$kCIInputEnableChromaticNoiseTrackingKey$kCIInputEnableEDRModeKey$kCIInputEnableSharpeningKey$kCIInputEnableVendorLensCorrectionKey$kCIInputExtentKey$kCIInputGradientImageKey$kCIInputIgnoreImageOrientationKey$kCIInputImageKey$kCIInputImageOrientationKey$kCIInputIntensityKey$kCIInputLinearSpaceFilter$kCIInputLuminanceNoiseReductionAmountKey$kCIInputMaskImageKey$kCIInputMatteImageKey$kCIInputMoireAmountKey$kCIInputNeutralChromaticityXKey$kCIInputNeutralChromaticityYKey$kCIInputNeutralLocationKey$kCIInputNeutralTemperatureKey$kCIInputNeutralTintKey$kCIInputNoiseReductionAmountKey$kCIInputNoiseReductionContrastAmountKey$kCIInputNoiseReductionDetailAmountKey$kCIInputNoiseReductionSharpnessAmountKey$kCIInputRadiusKey$kCIInputRefractionKey$kCIInputSaturationKey$kCIInputScaleFactorKey$kCIInputScaleKey$kCIInputShadingImageKey$kCIInputSharpnessKey$kCIInputTargetImageKey$kCIInputTimeKey$kCIInputTransformKey$kCIInputVersionKey$kCIInputWeightsKey$kCIInputWidthKey$kCIOutputImageKey$kCIOutputNativeSizeKey$kCISamplerAffineMatrix$kCISamplerColorSpace$kCISamplerFilterLinear$kCISamplerFilterMode$kCISamplerFilterNearest$kCISamplerWrapBlack$kCISamplerWrapClamp$kCISamplerWrapMode$kCISupportedDecoderVersionsKey$kCIUIParameterSet$kCIUISetAdvanced$kCIUISetBasic$kCIUISetDevelopment$kCIUISetIntermediate$"""
enums = """$CA_TEST@0$CA_WARN_DEPRECATED@1$CIDataMatrixCodeECCVersion000@0$CIDataMatrixCodeECCVersion050@50$CIDataMatrixCodeECCVersion080@80$CIDataMatrixCodeECCVersion100@100$CIDataMatrixCodeECCVersion140@140$CIDataMatrixCodeECCVersion200@200$CIQRCodeErrorCorrectionLevelH@72$CIQRCodeErrorCorrectionLevelL@76$CIQRCodeErrorCorrectionLevelM@77$CIQRCodeErrorCorrectionLevelQ@81$CIRenderDestinationAlphaNone@0$CIRenderDestinationAlphaPremultiplied@1$CIRenderDestinationAlphaUnpremultiplied@2$kCAConstraintHeight@7$kCAConstraintMaxX@2$kCAConstraintMaxY@6$kCAConstraintMidX@1$kCAConstraintMidY@5$kCAConstraintMinX@0$kCAConstraintMinY@4$kCAConstraintWidth@3$kCALayerBottomEdge@4$kCALayerHeightSizable@16$kCALayerLeftEdge@1$kCALayerMaxXMargin@4$kCALayerMaxXMaxYCorner@8$kCALayerMaxXMinYCorner@2$kCALayerMaxYMargin@32$kCALayerMinXMargin@1$kCALayerMinXMaxYCorner@4$kCALayerMinXMinYCorner@1$kCALayerMinYMargin@8$kCALayerNotSizable@0$kCALayerRightEdge@2$kCALayerTopEdge@8$kCALayerWidthSizable@2$"""
misc.update(
    {
        "CAEdgeAntialiasingMask": NewType("CAEdgeAntialiasingMask", int),
        "CAAutoresizingMask": NewType("CAAutoresizingMask", int),
        "CAConstraintAttribute": NewType("CAConstraintAttribute", int),
        "CACornerMask": NewType("CACornerMask", int),
    }
)
misc.update(
    {
        "CAEmitterLayerRenderMode": NewType("CAEmitterLayerRenderMode", str),
        "CAMediaTimingFillMode": NewType("CAMediaTimingFillMode", str),
        "CAShapeLayerFillRule": NewType("CAShapeLayerFillRule", str),
        "CALayerContentsGravity": NewType("CALayerContentsGravity", str),
        "CATextLayerAlignmentMode": NewType("CATextLayerAlignmentMode", str),
        "CAMediaTimingFunctionName": NewType("CAMediaTimingFunctionName", str),
        "CALayerCornerCurve": NewType("CALayerCornerCurve", str),
        "CAAnimationCalculationMode": NewType("CAAnimationCalculationMode", str),
        "CAToneMapMode": NewType("CAToneMapMode", str),
        "CAScrollLayerScrollMode": NewType("CAScrollLayerScrollMode", str),
        "CAEmitterLayerEmitterShape": NewType("CAEmitterLayerEmitterShape", str),
        "CAGradientLayerType": NewType("CAGradientLayerType", str),
        "CATransitionSubtype": NewType("CATransitionSubtype", str),
        "CATransitionType": NewType("CATransitionType", str),
        "CALayerContentsFilter": NewType("CALayerContentsFilter", str),
        "CAEmitterLayerEmitterMode": NewType("CAEmitterLayerEmitterMode", str),
        "CAValueFunctionName": NewType("CAValueFunctionName", str),
        "CAShapeLayerLineCap": NewType("CAShapeLayerLineCap", str),
        "CATextLayerTruncationMode": NewType("CATextLayerTruncationMode", str),
        "CAAnimationRotationMode": NewType("CAAnimationRotationMode", str),
        "CALayerContentsFormat": NewType("CALayerContentsFormat", str),
        "CAShapeLayerLineJoin": NewType("CAShapeLayerLineJoin", str),
    }
)
misc.update({})
functions = {
    "CATransform3DIsAffine": (b"B{CATransform3D=dddddddddddddddd}",),
    "CATransform3DInvert": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "CATransform3DIsIdentity": (b"B{CATransform3D=dddddddddddddddd}",),
    "CAFrameRateRangeMake": (b"{CAFrameRateRange=fff}fff",),
    "CATransform3DMakeScale": (b"{CATransform3D=dddddddddddddddd}ddd",),
    "CATransform3DTranslate": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}ddd",
    ),
    "CATransform3DEqualToTransform": (
        b"B{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "CATransform3DRotate": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}dddd",
    ),
    "CACurrentMediaTime": (b"d",),
    "CATransform3DMakeRotation": (b"{CATransform3D=dddddddddddddddd}dddd",),
    "CAFrameRateRangeIsEqualToRange": (
        b"B{CAFrameRateRange=fff}{CAFrameRateRange=fff}",
    ),
    "CATransform3DConcat": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "CATransform3DScale": (
        b"{CATransform3D=dddddddddddddddd}{CATransform3D=dddddddddddddddd}ddd",
    ),
    "CATransform3DMakeTranslation": (b"{CATransform3D=dddddddddddddddd}ddd",),
    "CATransform3DGetAffineTransform": (
        b"{CGAffineTransform=dddddd}{CATransform3D=dddddddddddddddd}",
    ),
    "CATransform3DMakeAffineTransform": (
        b"{CATransform3D=dddddddddddddddd}{CGAffineTransform=dddddd}",
    ),
}
aliases = {"CA_TESTABLE_CLASS": "CA_HIDDEN", "CA_TESTABLE": "CA_HIDDEN"}
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(b"CAAnimation", b"isRemovedOnCompletion", {"retval": {"type": b"Z"}})
    r(b"CAAnimation", b"setEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"CAAnimation", b"setRemovedOnCompletion:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAAnimation", b"shouldArchiveValueForKey:", {"retval": {"type": b"Z"}})
    r(
        b"CADisplayLink",
        b"displayLinkWithTarget:selector",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(b"CADisplayLink", b"isPaused", {"retval": {"type": b"Z"}})
    r(b"CADisplayLink", b"setPaused:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAEDRMetadata", b"isAvailable", {"retval": {"type": b"Z"}})
    r(b"CAEmitterBehavior", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"CAEmitterBehavior", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAEmitterCell", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"CAEmitterCell", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAEmitterCell", b"shouldArchiveValueForKey:", {"retval": {"type": b"Z"}})
    r(b"CAEmitterLayer", b"preservesDepth", {"retval": {"type": b"Z"}})
    r(b"CAEmitterLayer", b"setPreservesDepth:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"allowsEdgeAntialiasing", {"retval": {"type": "Z"}})
    r(b"CALayer", b"allowsGroupOpacity", {"retval": {"type": "Z"}})
    r(b"CALayer", b"containsPoint:", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"contentsAreFlipped", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"drawsAsynchronously", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"isDoubleSided", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"isGeometryFlipped", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"isHidden", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"isOpaque", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"masksToBounds", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"needsDisplay", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"needsDisplayForKey:", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"needsDisplayOnBoundsChange", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"needsLayout", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"setAllowsEdgeAntialiasing:", {"arguments": {2: {"type": "Z"}}})
    r(b"CALayer", b"setAllowsGroupOpacity:", {"arguments": {2: {"type": "Z"}}})
    r(b"CALayer", b"setDoubleSided:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setDrawsAsynchronously:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setGeometryFlipped:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setHidden:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setMasksToBounds:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setNeedsDisplayOnBoundsChange:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setOpaque:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setShouldRasterize:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CALayer", b"setWantsDynamicContentScaling:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CALayer",
        b"setWantsExtendedDynamicRangeContent:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CALayer", b"shouldArchiveValueForKey:", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"shouldRasterize", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"wantsDynamicContentScaling", {"retval": {"type": b"Z"}})
    r(b"CALayer", b"wantsExtendedDynamicRangeContent", {"retval": {"type": "Z"}})
    r(b"CAMetalDisplayLink", b"isPaused", {"retval": {"type": b"Z"}})
    r(b"CAMetalDisplayLink", b"setPaused:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAMetalLayer", b"allowsNextDrawableTimeout", {"retval": {"type": "Z"}})
    r(b"CAMetalLayer", b"displaySyncEnabled", {"retval": {"type": "Z"}})
    r(b"CAMetalLayer", b"framebufferOnly", {"retval": {"type": "Z"}})
    r(b"CAMetalLayer", b"presentsWithTransaction", {"retval": {"type": "Z"}})
    r(
        b"CAMetalLayer",
        b"setAllowsNextDrawableTimeout:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CAMetalLayer", b"setDisplaySyncEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"CAMetalLayer", b"setFramebufferOnly:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"CAMetalLayer",
        b"setPresentsWithTransaction:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"CAMetalLayer",
        b"setWantsExtendedDynamicRangeContent:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CAMetalLayer", b"wantsExtendedDynamicRangeContent", {"retval": {"type": "Z"}})
    r(
        b"CAOpenGLLayer",
        b"canDrawInCGLContext:pixelFormat:forLayerTime:displayTime:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                5: {
                    "type": b"^{CVTimeStamp=IiqQdq{CVSMPTETime=ssIIIssss}QQ}",
                    "type_modifier": b"n",
                }
            },
        },
    )
    r(
        b"CAOpenGLLayer",
        b"drawInCGLContext:pixelFormat:forLayerTime:displayTime:",
        {
            "arguments": {
                5: {
                    "type": b"^{CVTimeStamp=IiqQdq{CVSMPTETime=ssIIIssss}QQ}",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(b"CAOpenGLLayer", b"isAsynchronous", {"retval": {"type": b"Z"}})
    r(b"CAOpenGLLayer", b"setAsynchronous:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CAOpenGLLayer",
        b"setWantsExtendedDynamicRangeContent:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CAOpenGLLayer", b"wantsExtendedDynamicRangeContent", {"retval": {"type": "Z"}})
    r(b"CAPropertyAnimation", b"isAdditive", {"retval": {"type": b"Z"}})
    r(b"CAPropertyAnimation", b"isCumulative", {"retval": {"type": b"Z"}})
    r(b"CAPropertyAnimation", b"setAdditive:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CAPropertyAnimation", b"setCumulative:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CARenderer",
        b"beginFrameAtTime:timeStamp:",
        {
            "arguments": {
                3: {
                    "type": b"^{CVTimeStamp=IiqQdq{CVSMPTETime=ssIIIssss}QQ}",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"CARenderer",
        b"rendererWithCGLContext:options:",
        {"arguments": {2: {"type": "^{_CGLContextObject=}"}}},
    )
    r(b"CAReplicatorLayer", b"preservesDepth", {"retval": {"type": b"Z"}})
    r(b"CAReplicatorLayer", b"setPreservesDepth:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CASpringAnimation", b"allowsOverdamping", {"retval": {"type": b"Z"}})
    r(
        b"CASpringAnimation",
        b"setAllowsOverdamping:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"CATextLayer", b"allowsFontSubpixelQuantization", {"retval": {"type": "Z"}})
    r(b"CATextLayer", b"font", {"retval": {"type": b"@"}})
    r(b"CATextLayer", b"isWrapped", {"retval": {"type": b"Z"}})
    r(
        b"CATextLayer",
        b"setAllowsFontSubpixelQuantization:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CATextLayer", b"setFont:", {"arguments": {2: {"type": b"@"}}})
    r(b"CATextLayer", b"setWrapped:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CATransaction",
        b"completionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"CATransaction", b"disableActions", {"retval": {"type": b"Z"}})
    r(
        b"CATransaction",
        b"setCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"CATransaction", b"setDisableActions:", {"arguments": {2: {"type": b"Z"}}})
    r(b"CIAztecCodeDescriptor", b"isCompact", {"retval": {"type": "Z"}})
    r(b"CIColor", b"components", {"retval": {"c_array_of_variable_length": True}})
    r(
        b"CIContext",
        b"HEIF10RepresentationOfImage:colorSpace:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"OpenEXRRepresentationOfImage:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"createCGImage:fromRect:format:colorSpace:deferred:",
        {"retval": {"already_cfretained": True}, "arguments": {6: {"type": "Z"}}},
    )
    r(
        b"CIContext",
        b"createCGLayerWithSize:info:",
        {"retval": {"already_cfretained": True}},
    )
    r(
        b"CIContext",
        b"prepareRender:fromRect:toDestination:atPoint:error:",
        {"retval": {"type": "Z"}, "arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"render:toBitmap:rowBytes:bounds:format:colorSpace:",
        {"arguments": {3: {"type_modifier": b"o", "c_array_of_variable_length": True}}},
    )
    r(
        b"CIContext",
        b"startTaskToClear:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"startTaskToRender:fromRect:toDestination:atPoint:error:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"startTaskToRender:toDestination:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"writeHEIF10RepresentationOfImage:toURL:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"writeHEIFRepresentationOfImage:toURL:format:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {7: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"writeJPEGRepresentationOfImage:toURL:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"writeOpenEXRRepresentationOfImage:toURL:options:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"CIContext",
        b"writePNGRepresentationOfImage:toURL:format:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {7: {"type_modifier": b"o"}}},
    )
    r(b"CIFaceFeature", b"hasFaceAngle", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"hasLeftEyePosition", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"hasMouthPosition", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"hasRightEyePosition", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"hasSmile", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"hasTrackingFrameCount", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"hasTrackingID", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"leftEyeClosed", {"retval": {"type": b"Z"}})
    r(b"CIFaceFeature", b"rightEyeClosed", {"retval": {"type": b"Z"}})
    r(b"CIFilter", b"apply:", {"c_array_delimited_by_null": True, "variadic": True})
    r(
        b"CIFilter",
        b"filterArrayFromSerializedXMP:inputImageExtent:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"CIFilter",
        b"filterWithName:keysAndValues:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(b"CIFilter", b"isEnabled", {"retval": {"type": b"Z"}})
    r(b"CIFilter", b"setEnabled:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"CIFilterGenerator",
        b"writeToURL:atomically:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type": b"Z"}}},
    )
    r(b"CIFilterShape", b"transformBy:interior:", {"arguments": {3: {"type": b"Z"}}})
    r(
        b"CIImage",
        b"imageByApplyingTransform:highQualityDownsample:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"CIImage",
        b"imageWithTexture:size:flipped:colorSpace:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"CIImage",
        b"imageWithTexture:size:flipped:options:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"CIImage",
        b"initWithTexture:size:flipped:colorSpace:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"CIImage",
        b"initWithTexture:size:flipped:options:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(b"CIImage", b"isOpaque", {"retval": {"type": "Z"}})
    r(
        b"CIImage",
        b"writeHEIFRepresentationOfImage:toURL:format:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {7: {"type_modifier": b"o"}}},
    )
    r(
        b"CIImage",
        b"writeJPEGRepresentationOfImage:toURL:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"CIImage",
        b"writePNGRepresentationOfImage:toURL:format:colorSpace:options:error:",
        {"retval": {"type": "Z"}, "arguments": {7: {"type_modifier": b"o"}}},
    )
    r(
        b"CIImageProcessorKernel",
        b"applyWithExtent:inputs:arguments:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"CIImageProcessorKernel",
        b"processWithInputs:arguments:output:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"CIKernel",
        b"applyWithExtent:roiCallback:arguments:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"i"},
                            2: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"CIKernel",
        b"kernelWithFunctionName:fromMetalLibraryData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"CIKernel",
        b"kernelWithFunctionName:fromMetalLibraryData:outputPixelFormat:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"CIKernel",
        b"kernelsWithMetalString:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"CIKernel",
        b"setROISelector:",
        {
            "arguments": {
                2: {
                    "sel_of_type": b"{CGRect={CGPoint=dd}{CGSize=dd}}@:i{CGRect={CGPoint=dd}{CGSize=dd}}@"
                }
            }
        },
    )
    r(b"CIPDF417CodeDescriptor", b"isCompact", {"retval": {"type": "Z"}})
    r(
        b"CIPlugIn",
        b"loadPlugIn:allowExecutableCode:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"CIPlugIn",
        b"loadPlugIn:allowNonExecutable:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"CIRAWFilter", b"isColorNoiseReductionSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isContrastSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isDetailSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isGamutMappingEnabled", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isLensCorrectionEnabled", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isLensCorrectionSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isLocalToneMapSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isLuminanceNoiseReductionSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isMoireReductionSupported", {"retval": {"type": "Z"}})
    r(b"CIRAWFilter", b"isSharpnessSupported", {"retval": {"type": "Z"}})
    r(
        b"CIRAWFilter",
        b"setColorNoiseReductionSupported:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CIRAWFilter", b"setContrastSupported:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRAWFilter", b"setDetailSupported:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRAWFilter", b"setGamutMappingEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRAWFilter", b"setLensCorrectionEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRAWFilter", b"setLensCorrectionSupported:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRAWFilter", b"setLocalToneMapSupported:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"CIRAWFilter",
        b"setLuminanceNoiseReductionSupported:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CIRAWFilter", b"setMoireReductionSupported:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRAWFilter", b"setSharpnessSupported:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"CIRenderDestination",
        b"blendsInDestinationColorSpace",
        {"retval": {"type": "Z"}},
    )
    r(
        b"CIRenderDestination",
        b"initWithWidth:height:pixelFormat:commandBuffer:mtlTextureProvider:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"CIRenderDestination", b"isClamped", {"retval": {"type": "Z"}})
    r(b"CIRenderDestination", b"isDithered", {"retval": {"type": "Z"}})
    r(b"CIRenderDestination", b"isFlipped", {"retval": {"type": "Z"}})
    r(
        b"CIRenderDestination",
        b"setBlendsInDestinationColorSpace:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"CIRenderDestination", b"setClamped:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRenderDestination", b"setDithered:", {"arguments": {2: {"type": "Z"}}})
    r(b"CIRenderDestination", b"setFlipped:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"CIRenderTask",
        b"waitUntilCompletedAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"CISampler",
        b"initWithImage:keysAndValues:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"CISampler",
        b"samplerWithImage:keysAndValues:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"CIVector",
        b"initWithValues:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"CIVector",
        b"vectorWithValues:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"CIWarpKernel",
        b"applyWithExtent:roiCallback:inputImage:arguments:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"i"},
                            2: {"type": b"{CGRect={CGPoint=dd}{CGSize=dd}}"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSObject", b"EV", {"retval": {"type": "f"}})
    r(b"NSObject", b"NRNoiseLevel", {"retval": {"type": "f"}})
    r(b"NSObject", b"NRSharpness", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"actionForLayer:forKey:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"acuteAngle", {"retval": {"type": "f"}})
    r(b"NSObject", b"alwaysSpecifyCompaction", {"retval": {"type": "f"}})
    r(b"NSObject", b"amount", {"retval": {"type": "f"}})
    r(b"NSObject", b"angle", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"animationDidStart:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"animationDidStop:finished:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"Z"}},
        },
    )
    r(b"NSObject", b"aspectRatio", {"retval": {"type": "f"}})
    r(b"NSObject", b"autoreverses", {"required": True, "retval": {"type": b"Z"}})
    r(b"NSObject", b"barOffset", {"retval": {"type": "f"}})
    r(b"NSObject", b"barcodeHeight", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"baseAddress",
        {"retval": {"type": "^v", "c_array_of_variable_length": True}},
    )
    r(b"NSObject", b"beginTime", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"bias", {"retval": {"type": "f"}})
    r(b"NSObject", b"bottomHeight", {"retval": {"type": "f"}})
    r(b"NSObject", b"bottomLeft", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"bottomRight", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"breakpoint0", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"breakpoint1", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"brightness", {"retval": {"type": "f"}})
    r(b"NSObject", b"bytesPerRow", {"retval": {"type": "L"}})
    r(b"NSObject", b"centerStretchAmount", {"retval": {"type": "f"}})
    r(b"NSObject", b"closeness1", {"retval": {"type": "f"}})
    r(b"NSObject", b"closeness2", {"retval": {"type": "f"}})
    r(b"NSObject", b"closeness3", {"retval": {"type": "f"}})
    r(b"NSObject", b"compactStyle", {"retval": {"type": "f"}})
    r(b"NSObject", b"compactionMode", {"retval": {"type": "f"}})
    r(b"NSObject", b"compression", {"retval": {"type": "f"}})
    r(b"NSObject", b"concentration", {"retval": {"type": "f"}})
    r(b"NSObject", b"contrast", {"retval": {"type": "f"}})
    r(b"NSObject", b"contrast1", {"retval": {"type": "f"}})
    r(b"NSObject", b"contrast2", {"retval": {"type": "f"}})
    r(b"NSObject", b"contrast3", {"retval": {"type": "f"}})
    r(b"NSObject", b"correctionLevel", {"retval": {"type": "f"}})
    r(b"NSObject", b"count", {"retval": {"type": "q"}})
    r(b"NSObject", b"crop", {"retval": {"type": "B"}})
    r(b"NSObject", b"cropAmount", {"retval": {"type": "f"}})
    r(b"NSObject", b"crossAngle", {"retval": {"type": "f"}})
    r(b"NSObject", b"crossOpacity", {"retval": {"type": "f"}})
    r(b"NSObject", b"crossScale", {"retval": {"type": "f"}})
    r(b"NSObject", b"crossWidth", {"retval": {"type": "f"}})
    r(b"NSObject", b"cubeDimension", {"retval": {"type": "f"}})
    r(b"NSObject", b"dataColumns", {"retval": {"type": "f"}})
    r(b"NSObject", b"decay", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"displayLayer:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"dither", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"drawLayer:inContext:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"^{CGContext=}"}},
        },
    )
    r(b"NSObject", b"duration", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"edgeIntensity", {"retval": {"type": "f"}})
    r(b"NSObject", b"epsilon", {"retval": {"type": "f"}})
    r(b"NSObject", b"extent", {"retval": {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}})
    r(b"NSObject", b"fadeThreshold", {"retval": {"type": "f"}})
    r(b"NSObject", b"falloff", {"retval": {"type": "f"}})
    r(b"NSObject", b"fillMode", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"filterWithName:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"flipYTiles", {"retval": {"type": "B"}})
    r(b"NSObject", b"focalLength", {"retval": {"type": "f"}})
    r(b"NSObject", b"foldShadowAmount", {"retval": {"type": "f"}})
    r(b"NSObject", b"fontSize", {"retval": {"type": "f"}})
    r(b"NSObject", b"format", {"retval": {"type": "q"}})
    r(b"NSObject", b"gaussianSigma", {"retval": {"type": "f"}})
    r(b"NSObject", b"grayComponentReplacement", {"retval": {"type": "f"}})
    r(b"NSObject", b"growAmount", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"haloOverlap", {"retval": {"type": "f"}})
    r(b"NSObject", b"haloRadius", {"retval": {"type": "f"}})
    r(b"NSObject", b"haloWidth", {"retval": {"type": "f"}})
    r(b"NSObject", b"headIndex", {"retval": {"type": "f"}})
    r(b"NSObject", b"height", {"retval": {"type": "f"}})
    r(b"NSObject", b"highLimit", {"retval": {"type": "f"}})
    r(b"NSObject", b"highlightAmount", {"retval": {"type": "f"}})
    r(b"NSObject", b"hysteresisPasses", {"retval": {"type": "q"}})
    r(b"NSObject", b"insetPoint0", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"insetPoint1", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"intensity", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"invalidateLayoutOfLayer:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"layer", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"layerWillDraw:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"layers", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"layoutSublayersOfLayer:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"levels", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"load:",
        {"required": True, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"^v"}}},
    )
    r(b"NSObject", b"lowLimit", {"retval": {"type": "f"}})
    r(b"NSObject", b"lumaSigma", {"retval": {"type": "f"}})
    r(b"NSObject", b"maxHeight", {"retval": {"type": "f"}})
    r(b"NSObject", b"maxStriationRadius", {"retval": {"type": "f"}})
    r(b"NSObject", b"maxWidth", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"metalDisplayLink:needsUpdate:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"minHeight", {"retval": {"type": "f"}})
    r(b"NSObject", b"minWidth", {"retval": {"type": "f"}})
    r(b"NSObject", b"neutral", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"noiseLevel", {"retval": {"type": "f"}})
    r(b"NSObject", b"numberOfFolds", {"retval": {"type": "f"}})
    r(b"NSObject", b"opacity", {"retval": {"type": "f"}})
    r(b"NSObject", b"parameterB", {"retval": {"type": "f"}})
    r(b"NSObject", b"parameterC", {"retval": {"type": "f"}})
    r(b"NSObject", b"passes", {"retval": {"type": "f"}})
    r(b"NSObject", b"perceptual", {"retval": {"type": "B"}})
    r(b"NSObject", b"periodicity", {"retval": {"type": "f"}})
    r(b"NSObject", b"pitch", {"retval": {"type": "f"}})
    r(b"NSObject", b"point", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"point0", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"point1", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"point2", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"point3", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"point4", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"power", {"retval": {"type": "f"}})
    r(b"NSObject", b"preferredAspectRatio", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"preferredSizeOfLayer:",
        {
            "required": False,
            "retval": {"type": b"{CGSize=dd}"},
            "arguments": {2: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"provideImageData:bytesPerRow:origin::size::userInfo:",
        {
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "type": b"^v",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                },
                3: {"type": b"Q"},
                4: {"type": b"Q"},
                5: {"type": b"Q"},
                6: {"type": b"Q"},
                7: {"type": b"Q"},
                8: {"type": b"@"},
            },
        },
    )
    r(b"NSObject", b"quietSpace", {"retval": {"type": "f"}})
    r(b"NSObject", b"radius", {"retval": {"type": "f"}})
    r(b"NSObject", b"radius0", {"retval": {"type": "f"}})
    r(b"NSObject", b"radius1", {"retval": {"type": "f"}})
    r(b"NSObject", b"refraction", {"retval": {"type": "f"}})
    r(b"NSObject", b"region", {"retval": {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}})
    r(b"NSObject", b"repeatCount", {"required": True, "retval": {"type": b"f"}})
    r(b"NSObject", b"repeatDuration", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"ringAmount", {"retval": {"type": "f"}})
    r(b"NSObject", b"ringSize", {"retval": {"type": "f"}})
    r(b"NSObject", b"roll", {"retval": {"type": "f"}})
    r(b"NSObject", b"rotation", {"retval": {"type": "f"}})
    r(b"NSObject", b"rows", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"runActionForKey:object:arguments:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"saturation", {"retval": {"type": "f"}})
    r(b"NSObject", b"scaleFactor", {"retval": {"type": "f"}})
    r(b"NSObject", b"setAcuteAngle:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setAlwaysSpecifyCompaction:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setAngle:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setAspectRatio:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setAutoreverses:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSObject", b"setBarOffset:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setBarcodeHeight:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setBeginTime:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(b"NSObject", b"setBias:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setBottomHeight:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setBottomLeft:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setBottomRight:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setBreakpoint0:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setBreakpoint1:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setBrightness:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCenterStretchAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCloseness1:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCloseness2:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCloseness3:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCompactStyle:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCompactionMode:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCompression:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setConcentration:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setContrast1:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setContrast2:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setContrast3:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setContrast:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCorrectionLevel:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCount:", {"arguments": {2: {"type": "q"}}})
    r(b"NSObject", b"setCrop:", {"arguments": {2: {"type": "B"}}})
    r(b"NSObject", b"setCropAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCrossAngle:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCrossOpacity:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCrossScale:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCrossWidth:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setCubeDimension:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setDataColumns:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setDecay:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setDither:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setDuration:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(b"NSObject", b"setEV:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setEdgeIntensity:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setEpsilon:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setExtent:",
        {"arguments": {2: {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}}},
    )
    r(b"NSObject", b"setFadeThreshold:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setFalloff:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setFillMode:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"setFlipYTiles:", {"arguments": {2: {"type": "B"}}})
    r(b"NSObject", b"setFocalLength:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setFoldShadowAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setFontSize:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setGaussianSigma:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setGrayComponentReplacement:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setGrowAmount:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setHaloOverlap:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHaloRadius:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHaloWidth:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHeadIndex:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHeight:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHighLimit:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHighlightAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setHysteresisPasses:", {"arguments": {2: {"type": "q"}}})
    r(b"NSObject", b"setInsetPoint0:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setInsetPoint1:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setIntensity:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setLayers:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setLevels:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setLowLimit:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setLumaSigma:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setMaxHeight:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setMaxStriationRadius:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setMaxWidth:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setMinHeight:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setMinWidth:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setNRNoiseLevel:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setNRSharpness:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setNeutral:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setNoiseLevel:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setNumberOfFolds:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setOpacity:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setParameterB:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setParameterC:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setPasses:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setPerceptual:", {"arguments": {2: {"type": "B"}}})
    r(b"NSObject", b"setPeriodicity:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setPitch:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setPoint0:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setPoint1:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setPoint2:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setPoint3:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setPoint4:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setPoint:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setPower:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setPreferredAspectRatio:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setQuietSpace:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRadius0:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRadius1:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRadius:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRefraction:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setRepeatCount:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(
        b"NSObject",
        b"setRepeatDuration:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(b"NSObject", b"setRingAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRingSize:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRoll:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRotation:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setRows:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSaturation:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setScaleFactor:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSftmaxNormalization:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setShadowAmount:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setShadowDensity:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setShadowExtent:",
        {"arguments": {2: {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}}},
    )
    r(b"NSObject", b"setShadowOffset:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setShadowRadius:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setShadowSize:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSharpness:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSigma:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSoftmaxNormalization:", {"arguments": {2: {"type": "B"}}})
    r(b"NSObject", b"setSoftness:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSourceHeadroom:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSpatialSigma:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setSpeed:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"f"}}},
    )
    r(b"NSObject", b"setStrands:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setStriationContrast:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setStriationStrength:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setSunRadius:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setTargetHeadroom:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setTargetNeutral:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setThreshold:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setThresholdHigh:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setThresholdLow:", {"arguments": {2: {"type": "f"}}})
    r(
        b"NSObject",
        b"setTimeOffset:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"d"}}},
    )
    r(b"NSObject", b"setTopLeft:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSObject", b"setTopRight:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(
        b"NSObject",
        b"setTransform:",
        {"arguments": {2: {"type": "{CGAffineTransform=dddddd}"}}},
    )
    r(b"NSObject", b"setUnderColorRemoval:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setUnsharpMaskIntensity:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setUnsharpMaskRadius:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setValue:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setWidth:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setYaw:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"setZoom:", {"arguments": {2: {"type": "f"}}})
    r(b"NSObject", b"shadowAmount", {"retval": {"type": "f"}})
    r(b"NSObject", b"shadowDensity", {"retval": {"type": "f"}})
    r(
        b"NSObject",
        b"shadowExtent",
        {"retval": {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}},
    )
    r(b"NSObject", b"shadowOffset", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"shadowRadius", {"retval": {"type": "f"}})
    r(b"NSObject", b"shadowSize", {"retval": {"type": "f"}})
    r(b"NSObject", b"sharpness", {"retval": {"type": "f"}})
    r(b"NSObject", b"sigma", {"retval": {"type": "f"}})
    r(b"NSObject", b"softmaxNormalization", {"retval": {"type": "B"}})
    r(b"NSObject", b"softness", {"retval": {"type": "f"}})
    r(b"NSObject", b"sourceHeadroom", {"retval": {"type": "f"}})
    r(b"NSObject", b"spatialSigma", {"retval": {"type": "f"}})
    r(b"NSObject", b"speed", {"required": True, "retval": {"type": b"f"}})
    r(b"NSObject", b"strands", {"retval": {"type": "f"}})
    r(b"NSObject", b"striationContrast", {"retval": {"type": "f"}})
    r(b"NSObject", b"striationStrength", {"retval": {"type": "f"}})
    r(b"NSObject", b"sunRadius", {"retval": {"type": "f"}})
    r(b"NSObject", b"targetHeadroom", {"retval": {"type": "f"}})
    r(b"NSObject", b"targetNeutral", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"texture", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"threshold", {"retval": {"type": "f"}})
    r(b"NSObject", b"thresholdHigh", {"retval": {"type": "f"}})
    r(b"NSObject", b"thresholdLow", {"retval": {"type": "f"}})
    r(b"NSObject", b"timeOffset", {"required": True, "retval": {"type": b"d"}})
    r(b"NSObject", b"topLeft", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"topRight", {"retval": {"type": "{CGPoint=dd}"}})
    r(b"NSObject", b"transform", {"retval": {"type": "{CGAffineTransform=dddddd}"}})
    r(b"NSObject", b"underColorRemoval", {"retval": {"type": "f"}})
    r(b"NSObject", b"unsharpMaskIntensity", {"retval": {"type": "f"}})
    r(b"NSObject", b"unsharpMaskRadius", {"retval": {"type": "f"}})
    r(b"NSObject", b"value", {"retval": {"type": "f"}})
    r(b"NSObject", b"width", {"retval": {"type": "f"}})
    r(b"NSObject", b"yaw", {"retval": {"type": "f"}})
    r(b"NSObject", b"zoom", {"retval": {"type": "f"}})
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector(
    "CAConstraint", b"initWithAttribute:relativeTo:attribute:scale:offset:"
)
objc.registerNewKeywordsFromSelector("CAEmitterBehavior", b"initWithType:")
objc.registerNewKeywordsFromSelector("CALayer", b"initWithLayer:")
objc.registerNewKeywordsFromSelector(
    "CAMediaTimingFunction", b"initWithControlPoints::::"
)
objc.registerNewKeywordsFromSelector("CAMetalDisplayLink", b"initWithMetalLayer:")
objc.registerNewKeywordsFromSelector("CARemoteLayerClient", b"initWithServerPort:")
objc.registerNewKeywordsFromSelector(
    "CASpringAnimation", b"initWithPerceptualDuration:bounce:"
)
objc.registerNewKeywordsFromSelector("CIColor", b"initWithCGColor:")
objc.registerNewKeywordsFromSelector("CIFilterGenerator", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("CIFilterShape", b"initWithRect:")
objc.registerNewKeywordsFromSelector(
    "CIImage", b"initWithBitmapData:bytesPerRow:size:format:colorSpace:"
)
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCGImage:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCGImage:options:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCGLayer:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCGLayer:options:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCVImageBuffer:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCVImageBuffer:options:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCVPixelBuffer:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithCVPixelBuffer:options:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithColor:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithContentsOfURL:options:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithData:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithData:options:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithIOSurface:")
objc.registerNewKeywordsFromSelector("CIImage", b"initWithIOSurface:options:")
objc.registerNewKeywordsFromSelector(
    "CIImage", b"initWithIOSurface:plane:format:options:"
)
objc.registerNewKeywordsFromSelector(
    "CIImage", b"initWithImageProvider:size::format:colorSpace:options:"
)
objc.registerNewKeywordsFromSelector(
    "CIImage", b"initWithTexture:size:flipped:colorSpace:"
)
objc.registerNewKeywordsFromSelector(
    "CIImage", b"initWithTexture:size:flipped:options:"
)
objc.registerNewKeywordsFromSelector("CIImageAccumulator", b"initWithExtent:format:")
objc.registerNewKeywordsFromSelector(
    "CIImageAccumulator", b"initWithExtent:format:colorSpace:"
)
objc.registerNewKeywordsFromSelector("CISampler", b"initWithImage:")
objc.registerNewKeywordsFromSelector("CISampler", b"initWithImage:keysAndValues:")
objc.registerNewKeywordsFromSelector("CISampler", b"initWithImage:options:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithCGAffineTransform:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithCGPoint:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithCGRect:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithString:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithValues:count:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithX:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithX:Y:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithX:Y:Z:")
objc.registerNewKeywordsFromSelector("CIVector", b"initWithX:Y:Z:W:")
protocols = {
    "CAAnimationDelegate": objc.informal_protocol(
        "CAAnimationDelegate",
        [
            objc.selector(None, b"animationDidStart:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"animationDidStop:finished:", b"v@:@Z", isRequired=False
            ),
        ],
    ),
    "CALayerDelegate": objc.informal_protocol(
        "CALayerDelegate",
        [
            objc.selector(
                None, b"drawLayer:inContext:", b"v@:@^{CGContext=}", isRequired=False
            ),
            objc.selector(None, b"actionForLayer:forKey:", b"@@:@@", isRequired=False),
            objc.selector(None, b"displayLayer:", b"v@:@", isRequired=False),
            objc.selector(None, b"layoutSublayersOfLayer:", b"v@:@", isRequired=False),
        ],
    ),
    "CIImageProvider": objc.informal_protocol(
        "CIImageProvider",
        [
            objc.selector(
                None,
                b"provideImageData:bytesPerRow:origin::size::userInfo:",
                b"v@:^vQQQQQ@",
                isRequired=False,
            )
        ],
    ),
    "CALayoutManager": objc.informal_protocol(
        "CALayoutManager",
        [
            objc.selector(
                None, b"preferredSizeOfLayer:", b"{CGSize=dd}@:@", isRequired=False
            ),
            objc.selector(None, b"layoutSublayersOfLayer:", b"v@:@", isRequired=False),
            objc.selector(None, b"invalidateLayoutOfLayer:", b"v@:@", isRequired=False),
        ],
    ),
}
expressions = {}

# END OF FILE
