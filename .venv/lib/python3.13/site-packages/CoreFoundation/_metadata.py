# This file is generated by objective.metadata
#
# Last update: Sat Mar  1 11:17:24 2025
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "CGPoint": objc.createStructType(
            "CoreFoundation.CGPoint", b"{CGPoint=dd}", ["x", "y"]
        ),
        "CFXMLExternalID": objc.createStructType(
            "CoreFoundation.CFXMLExternalID",
            b"{CFXMLExternalID=^{__CFURL=}^{__CFString=}}",
            ["systemID", "publicID"],
        ),
        "CFXMLAttributeDeclarationInfo": objc.createStructType(
            "CoreFoundation.CFXMLAttributeDeclarationInfo",
            b"{CFXMLAttributeDeclarationInfo=^{__CFString=}^{__CFString=}^{__CFString=}}",
            ["attributeName", "typeString", "defaultString"],
        ),
        "CFGregorianDate": objc.createStructType(
            "CoreFoundation.CFGregorianDate",
            b"{CFGregorianDate=iccccd}",
            ["year", "month", "day", "hour", "minute", "second"],
        ),
        "CFXMLDocumentTypeInfo": objc.createStructType(
            "CoreFoundation.CFXMLDocumentTypeInfo",
            b"{CFXMLDocumentTypeInfo={CFXMLExternalID=^{__CFURL=}^{__CFString=}}}",
            ["externalID"],
        ),
        "CGVector": objc.createStructType(
            "CoreFoundation.CGVector", b"{CGVector=dd}", ["dx", "dy"]
        ),
        "CGSize": objc.createStructType(
            "CoreFoundation.CGSize", b"{CGSize=dd}", ["width", "height"]
        ),
        "CFSocketSignature": objc.createStructType(
            "CoreFoundation.CFSocketSignature",
            b"{CFSocketSignature=iii^{__CFData=}}",
            ["protocolFamily", "socketType", "protocol", "address"],
        ),
        "CFXMLDocumentInfo": objc.createStructType(
            "CoreFoundation.CFXMLDocumentInfo",
            b"{CFXMLDocumentInfo=^{__CFURL=}I}",
            ["sourceURL", "encoding"],
        ),
        "CFXMLElementInfo": objc.createStructType(
            "CoreFoundation.CFXMLElementInfo",
            b"{CFXMLElementInfo=^{__CFDictionary=}^{__CFArray=}Z[3c]}",
            ["attributes", "attributeOrder", "isEmpty", "_reserved"],
        ),
        "CFUUIDBytes": objc.createStructType(
            "CoreFoundation.CFUUIDBytes",
            b"{CFUUIDBytes=CCCCCCCCCCCCCCCC}",
            [
                "byte0",
                "byte1",
                "byte2",
                "byte3",
                "byte4",
                "byte5",
                "byte6",
                "byte7",
                "byte8",
                "byte9",
                "byte10",
                "byte11",
                "byte12",
                "byte13",
                "byte14",
                "byte15",
            ],
        ),
        "CFSwappedFloat32": objc.createStructType(
            "CoreFoundation.CFSwappedFloat32", b"{CFSwappedFloat32=I}", ["v"]
        ),
        "CFXMLEntityReferenceInfo": objc.createStructType(
            "CoreFoundation.CFXMLEntityReferenceInfo",
            b"{CFXMLEntityReferenceInfo=q}",
            ["entityType"],
        ),
        "CFXMLProcessingInstructionInfo": objc.createStructType(
            "CoreFoundation.CFXMLProcessingInstructionInfo",
            b"{CFXMLProcessingInstructionInfo=^{__CFString=}}",
            ["dataString"],
        ),
        "CFRange": objc.createStructType(
            "CoreFoundation.CFRange", b"{CFRange=qq}", ["location", "length"]
        ),
        "CGRect": objc.createStructType(
            "CoreFoundation.CGRect",
            b"{CGRect={CGPoint=dd}{CGSize=dd}}",
            ["origin", "size"],
        ),
        "CFStreamError": objc.createStructType(
            "CoreFoundation.CFStreamError", b"{CFStreamError=qi}", ["domain", "error"]
        ),
        "CFXMLAttributeListDeclarationInfo": objc.createStructType(
            "CoreFoundation.CFXMLAttributeListDeclarationInfo",
            b"{CFXMLAttributeListDeclarationInfo=q^{CFXMLAttributeDeclarationInfo=^{__CFString=}^{__CFString=}^{__CFString=}}}",
            ["numberOfAttributes", "attributes"],
        ),
        "CFGregorianUnits": objc.createStructType(
            "CoreFoundation.CFGregorianUnits",
            b"{CFGregorianUnits=iiiiid}",
            ["years", "months", "days", "hours", "minutes", "seconds"],
        ),
        "CFXMLElementTypeDeclarationInfo": objc.createStructType(
            "CoreFoundation.CFXMLElementTypeDeclarationInfo",
            b"{CFXMLElementTypeDeclarationInfo=^{__CFString=}}",
            ["contentDescription"],
        ),
        "CFXMLEntityInfo": objc.createStructType(
            "CoreFoundation.CFXMLEntityInfo",
            b"{CFXMLEntityInfo=q^{__CFString=}{CFXMLExternalID=^{__CFURL=}^{__CFString=}}^{__CFString=}}",
            ["entityType", "replacementText", "entityID", "notationName"],
        ),
        "CFXMLNotationInfo": objc.createStructType(
            "CoreFoundation.CFXMLNotationInfo",
            b"{CFXMLNotationInfo={CFXMLExternalID=^{__CFURL=}^{__CFString=}}}",
            ["externalID"],
        ),
        "CGAffineTransformComponents": objc.createStructType(
            "CoreFoundation.CGAffineTransformComponents",
            b"{CGAffineTransformComponents={CGSize=dd}dd{CGVector=dd}}",
            ["scale", "horizontalShear", "rotation", "translation"],
        ),
        "CFSwappedFloat64": objc.createStructType(
            "CoreFoundation.CFSwappedFloat64", b"{CFSwappedFloat64=Q}", ["v"]
        ),
        "CGAffineTransform": objc.createStructType(
            "CoreFoundation.CGAffineTransform",
            b"{CGAffineTransform=dddddd}",
            ["a", "b", "c", "d", "tx", "ty"],
        ),
    }
)
constants = """$kCFAbsoluteTimeIntervalSince1904@d$kCFAbsoluteTimeIntervalSince1970@d$kCFAllocatorDefault@^{__CFAllocator=}$kCFAllocatorMalloc@^{__CFAllocator=}$kCFAllocatorMallocZone@^{__CFAllocator=}$kCFAllocatorNull@^{__CFAllocator=}$kCFAllocatorSystemDefault@^{__CFAllocator=}$kCFAllocatorUseContext@^{__CFAllocator=}$kCFBooleanFalse@^{__CFBoolean=}$kCFBooleanTrue@^{__CFBoolean=}$kCFBuddhistCalendar$kCFBundleDevelopmentRegionKey$kCFBundleExecutableKey$kCFBundleIdentifierKey$kCFBundleInfoDictionaryVersionKey$kCFBundleLocalizationsKey$kCFBundleNameKey$kCFBundleVersionKey$kCFChineseCalendar$kCFCoreFoundationVersionNumber@d$kCFDateFormatterAMSymbol$kCFDateFormatterCalendar$kCFDateFormatterCalendarName$kCFDateFormatterDefaultDate$kCFDateFormatterDefaultFormat$kCFDateFormatterDoesRelativeDateFormattingKey$kCFDateFormatterEraSymbols$kCFDateFormatterGregorianStartDate$kCFDateFormatterIsLenient$kCFDateFormatterLongEraSymbols$kCFDateFormatterMonthSymbols$kCFDateFormatterPMSymbol$kCFDateFormatterQuarterSymbols$kCFDateFormatterShortMonthSymbols$kCFDateFormatterShortQuarterSymbols$kCFDateFormatterShortStandaloneMonthSymbols$kCFDateFormatterShortStandaloneQuarterSymbols$kCFDateFormatterShortStandaloneWeekdaySymbols$kCFDateFormatterShortWeekdaySymbols$kCFDateFormatterStandaloneMonthSymbols$kCFDateFormatterStandaloneQuarterSymbols$kCFDateFormatterStandaloneWeekdaySymbols$kCFDateFormatterTimeZone$kCFDateFormatterTwoDigitStartDate$kCFDateFormatterVeryShortMonthSymbols$kCFDateFormatterVeryShortStandaloneMonthSymbols$kCFDateFormatterVeryShortStandaloneWeekdaySymbols$kCFDateFormatterVeryShortWeekdaySymbols$kCFDateFormatterWeekdaySymbols$kCFErrorDescriptionKey$kCFErrorDomainCocoa$kCFErrorDomainMach$kCFErrorDomainOSStatus$kCFErrorDomainPOSIX$kCFErrorFilePathKey$kCFErrorLocalizedDescriptionKey$kCFErrorLocalizedFailureKey$kCFErrorLocalizedFailureReasonKey$kCFErrorLocalizedRecoverySuggestionKey$kCFErrorURLKey$kCFErrorUnderlyingErrorKey$kCFGregorianCalendar$kCFHebrewCalendar$kCFISO8601Calendar$kCFIndianCalendar$kCFIslamicCalendar$kCFIslamicCivilCalendar$kCFIslamicTabularCalendar$kCFIslamicUmmAlQuraCalendar$kCFJapaneseCalendar$kCFLocaleAlternateQuotationBeginDelimiterKey$kCFLocaleAlternateQuotationEndDelimiterKey$kCFLocaleCalendar$kCFLocaleCalendarIdentifier$kCFLocaleCollationIdentifier$kCFLocaleCollatorIdentifier$kCFLocaleCountryCode$kCFLocaleCountryCodeKey$kCFLocaleCurrencyCode$kCFLocaleCurrencySymbol$kCFLocaleCurrentLocaleDidChangeNotification$kCFLocaleDecimalSeparator$kCFLocaleExemplarCharacterSet$kCFLocaleGroupingSeparator$kCFLocaleIdentifier$kCFLocaleLanguageCode$kCFLocaleLanguageCodeKey$kCFLocaleMeasurementSystem$kCFLocaleQuotationBeginDelimiterKey$kCFLocaleQuotationEndDelimiterKey$kCFLocaleScriptCode$kCFLocaleUsesMetricSystem$kCFLocaleVariantCode$kCFNull@^{__CFNull=}$kCFNumberFormatterAlwaysShowDecimalSeparator$kCFNumberFormatterCurrencyCode$kCFNumberFormatterCurrencyDecimalSeparator$kCFNumberFormatterCurrencyGroupingSeparator$kCFNumberFormatterCurrencySymbol$kCFNumberFormatterDecimalSeparator$kCFNumberFormatterDefaultFormat$kCFNumberFormatterExponentSymbol$kCFNumberFormatterFormatWidth$kCFNumberFormatterGroupingSeparator$kCFNumberFormatterGroupingSize$kCFNumberFormatterInfinitySymbol$kCFNumberFormatterInternationalCurrencySymbol$kCFNumberFormatterIsLenient$kCFNumberFormatterMaxFractionDigits$kCFNumberFormatterMaxIntegerDigits$kCFNumberFormatterMaxSignificantDigits$kCFNumberFormatterMinFractionDigits$kCFNumberFormatterMinGroupingDigits$kCFNumberFormatterMinIntegerDigits$kCFNumberFormatterMinSignificantDigits$kCFNumberFormatterMinusSign$kCFNumberFormatterMultiplier$kCFNumberFormatterNaNSymbol$kCFNumberFormatterNegativePrefix$kCFNumberFormatterNegativeSuffix$kCFNumberFormatterPaddingCharacter$kCFNumberFormatterPaddingPosition$kCFNumberFormatterPerMillSymbol$kCFNumberFormatterPercentSymbol$kCFNumberFormatterPlusSign$kCFNumberFormatterPositivePrefix$kCFNumberFormatterPositiveSuffix$kCFNumberFormatterRoundingIncrement$kCFNumberFormatterRoundingMode$kCFNumberFormatterSecondaryGroupingSize$kCFNumberFormatterUseGroupingSeparator$kCFNumberFormatterUseSignificantDigits$kCFNumberFormatterZeroSymbol$kCFNumberNaN@^{__CFNumber=}$kCFNumberNegativeInfinity@^{__CFNumber=}$kCFNumberPositiveInfinity@^{__CFNumber=}$kCFPersianCalendar$kCFPreferencesAnyApplication$kCFPreferencesAnyHost$kCFPreferencesAnyUser$kCFPreferencesCurrentApplication$kCFPreferencesCurrentHost$kCFPreferencesCurrentUser$kCFRepublicOfChinaCalendar$kCFRunLoopCommonModes$kCFRunLoopDefaultMode$kCFSocketCommandKey$kCFSocketErrorKey$kCFSocketNameKey$kCFSocketRegisterCommand$kCFSocketResultKey$kCFSocketRetrieveCommand$kCFSocketValueKey$kCFStreamErrorDomainSOCKS@i$kCFStreamErrorDomainSSL@i$kCFStreamPropertyAppendToFile$kCFStreamPropertyDataWritten$kCFStreamPropertyFileCurrentOffset$kCFStreamPropertySOCKSPassword$kCFStreamPropertySOCKSProxy$kCFStreamPropertySOCKSProxyHost$kCFStreamPropertySOCKSProxyPort$kCFStreamPropertySOCKSUser$kCFStreamPropertySOCKSVersion$kCFStreamPropertyShouldCloseNativeSocket$kCFStreamPropertySocketNativeHandle$kCFStreamPropertySocketRemoteHostName$kCFStreamPropertySocketRemotePortNumber$kCFStreamPropertySocketSecurityLevel$kCFStreamSocketSOCKSVersion4$kCFStreamSocketSOCKSVersion5$kCFStreamSocketSecurityLevelNegotiatedSSL$kCFStreamSocketSecurityLevelNone$kCFStreamSocketSecurityLevelSSLv2$kCFStreamSocketSecurityLevelSSLv3$kCFStreamSocketSecurityLevelTLSv1$kCFStringTransformFullwidthHalfwidth$kCFStringTransformHiraganaKatakana$kCFStringTransformLatinArabic$kCFStringTransformLatinCyrillic$kCFStringTransformLatinGreek$kCFStringTransformLatinHangul$kCFStringTransformLatinHebrew$kCFStringTransformLatinHiragana$kCFStringTransformLatinKatakana$kCFStringTransformLatinThai$kCFStringTransformMandarinLatin$kCFStringTransformStripCombiningMarks$kCFStringTransformStripDiacritics$kCFStringTransformToLatin$kCFStringTransformToUnicodeName$kCFStringTransformToXMLHex$kCFTimeZoneSystemTimeZoneDidChangeNotification$kCFURLAddedToDirectoryDateKey$kCFURLApplicationIsScriptableKey$kCFURLAttributeModificationDateKey$kCFURLCanonicalPathKey$kCFURLContentAccessDateKey$kCFURLContentModificationDateKey$kCFURLCreationDateKey$kCFURLCustomIconKey$kCFURLDirectoryEntryCountKey$kCFURLDocumentIdentifierKey$kCFURLEffectiveIconKey$kCFURLFileAllocatedSizeKey$kCFURLFileContentIdentifierKey$kCFURLFileDirectoryContents$kCFURLFileExists$kCFURLFileIdentifierKey$kCFURLFileLastModificationTime$kCFURLFileLength$kCFURLFileOwnerID$kCFURLFilePOSIXMode$kCFURLFileProtectionComplete$kCFURLFileProtectionCompleteUnlessOpen$kCFURLFileProtectionCompleteUntilFirstUserAuthentication$kCFURLFileProtectionCompleteWhenUserInactive$kCFURLFileProtectionKey$kCFURLFileProtectionNone$kCFURLFileResourceIdentifierKey$kCFURLFileResourceTypeBlockSpecial$kCFURLFileResourceTypeCharacterSpecial$kCFURLFileResourceTypeDirectory$kCFURLFileResourceTypeKey$kCFURLFileResourceTypeNamedPipe$kCFURLFileResourceTypeRegular$kCFURLFileResourceTypeSocket$kCFURLFileResourceTypeSymbolicLink$kCFURLFileResourceTypeUnknown$kCFURLFileSecurityKey$kCFURLFileSizeKey$kCFURLGenerationIdentifierKey$kCFURLHTTPStatusCode$kCFURLHTTPStatusLine$kCFURLHasHiddenExtensionKey$kCFURLIsAliasFileKey$kCFURLIsApplicationKey$kCFURLIsDirectoryKey$kCFURLIsExcludedFromBackupKey$kCFURLIsExecutableKey$kCFURLIsHiddenKey$kCFURLIsMountTriggerKey$kCFURLIsPackageKey$kCFURLIsPurgeableKey$kCFURLIsReadableKey$kCFURLIsRegularFileKey$kCFURLIsSparseKey$kCFURLIsSymbolicLinkKey$kCFURLIsSystemImmutableKey$kCFURLIsUbiquitousItemKey$kCFURLIsUserImmutableKey$kCFURLIsVolumeKey$kCFURLIsWritableKey$kCFURLKeysOfUnsetValuesKey$kCFURLLabelColorKey$kCFURLLabelNumberKey$kCFURLLinkCountKey$kCFURLLocalizedLabelKey$kCFURLLocalizedNameKey$kCFURLLocalizedTypeDescriptionKey$kCFURLMayHaveExtendedAttributesKey$kCFURLMayShareFileContentKey$kCFURLNameKey$kCFURLParentDirectoryURLKey$kCFURLPathKey$kCFURLPreferredIOBlockSizeKey$kCFURLQuarantinePropertiesKey$kCFURLTagNamesKey$kCFURLTotalFileAllocatedSizeKey$kCFURLTotalFileSizeKey$kCFURLTypeIdentifierKey$kCFURLUbiquitousItemDownloadingErrorKey$kCFURLUbiquitousItemDownloadingStatusCurrent$kCFURLUbiquitousItemDownloadingStatusDownloaded$kCFURLUbiquitousItemDownloadingStatusKey$kCFURLUbiquitousItemDownloadingStatusNotDownloaded$kCFURLUbiquitousItemHasUnresolvedConflictsKey$kCFURLUbiquitousItemIsDownloadedKey$kCFURLUbiquitousItemIsDownloadingKey$kCFURLUbiquitousItemIsExcludedFromSyncKey$kCFURLUbiquitousItemIsUploadedKey$kCFURLUbiquitousItemIsUploadingKey$kCFURLUbiquitousItemPercentDownloadedKey$kCFURLUbiquitousItemPercentUploadedKey$kCFURLUbiquitousItemUploadingErrorKey$kCFURLVolumeAvailableCapacityForImportantUsageKey$kCFURLVolumeAvailableCapacityForOpportunisticUsageKey$kCFURLVolumeAvailableCapacityKey$kCFURLVolumeCreationDateKey$kCFURLVolumeIdentifierKey$kCFURLVolumeIsAutomountedKey$kCFURLVolumeIsBrowsableKey$kCFURLVolumeIsEjectableKey$kCFURLVolumeIsEncryptedKey$kCFURLVolumeIsInternalKey$kCFURLVolumeIsJournalingKey$kCFURLVolumeIsLocalKey$kCFURLVolumeIsReadOnlyKey$kCFURLVolumeIsRemovableKey$kCFURLVolumeIsRootFileSystemKey$kCFURLVolumeLocalizedFormatDescriptionKey$kCFURLVolumeLocalizedNameKey$kCFURLVolumeMaximumFileSizeKey$kCFURLVolumeMountFromLocationKey$kCFURLVolumeNameKey$kCFURLVolumeResourceCountKey$kCFURLVolumeSubtypeKey$kCFURLVolumeSupportsAccessPermissionsKey$kCFURLVolumeSupportsAdvisoryFileLockingKey$kCFURLVolumeSupportsCasePreservedNamesKey$kCFURLVolumeSupportsCaseSensitiveNamesKey$kCFURLVolumeSupportsCompressionKey$kCFURLVolumeSupportsExclusiveRenamingKey$kCFURLVolumeSupportsExtendedSecurityKey$kCFURLVolumeSupportsFileCloningKey$kCFURLVolumeSupportsFileProtectionKey$kCFURLVolumeSupportsHardLinksKey$kCFURLVolumeSupportsImmutableFilesKey$kCFURLVolumeSupportsJournalingKey$kCFURLVolumeSupportsPersistentIDsKey$kCFURLVolumeSupportsRenamingKey$kCFURLVolumeSupportsRootDirectoryDatesKey$kCFURLVolumeSupportsSparseFilesKey$kCFURLVolumeSupportsSwapRenamingKey$kCFURLVolumeSupportsSymbolicLinksKey$kCFURLVolumeSupportsVolumeSizesKey$kCFURLVolumeSupportsZeroRunsKey$kCFURLVolumeTotalCapacityKey$kCFURLVolumeTypeNameKey$kCFURLVolumeURLForRemountingKey$kCFURLVolumeURLKey$kCFURLVolumeUUIDStringKey$kCFUserNotificationAlertHeaderKey$kCFUserNotificationAlertMessageKey$kCFUserNotificationAlertTopMostKey$kCFUserNotificationAlternateButtonTitleKey$kCFUserNotificationCheckBoxTitlesKey$kCFUserNotificationDefaultButtonTitleKey$kCFUserNotificationIconURLKey$kCFUserNotificationKeyboardTypesKey$kCFUserNotificationLocalizationURLKey$kCFUserNotificationOtherButtonTitleKey$kCFUserNotificationPopUpSelectionKey$kCFUserNotificationPopUpTitlesKey$kCFUserNotificationProgressIndicatorValueKey$kCFUserNotificationSoundURLKey$kCFUserNotificationTextFieldTitlesKey$kCFUserNotificationTextFieldValuesKey$kCFXMLTreeErrorDescription$kCFXMLTreeErrorLineNumber$kCFXMLTreeErrorLocation$kCFXMLTreeErrorStatusCode$"""
enums = """$CFByteOrderBigEndian@2$CFByteOrderLittleEndian@1$CFByteOrderUnknown@0$CFNotificationSuspensionBehaviorCoalesce@2$CFNotificationSuspensionBehaviorDeliverImmediately@4$CFNotificationSuspensionBehaviorDrop@1$CFNotificationSuspensionBehaviorHold@3$CF_HAS_TYPED_ALLOCATOR@0$CF_USE_OSBYTEORDER_H@1$CGFLOAT_DEFINED@1$CGFLOAT_IS_DOUBLE@1$CGRectMaxXEdge@2$CGRectMaxYEdge@3$CGRectMinXEdge@0$CGRectMinYEdge@1$CGVECTOR_DEFINED@1$COREFOUNDATION_CFPLUGINCOM_SEPARATE@1$FALSE@0$ISA_PTRAUTH_DISCRIMINATOR@27361$TRUE@1$kCFBookmarkResolutionWithoutMountingMask@512$kCFBookmarkResolutionWithoutUIMask@256$kCFBundleExecutableArchitectureARM64@16777228$kCFBundleExecutableArchitectureI386@7$kCFBundleExecutableArchitecturePPC@18$kCFBundleExecutableArchitecturePPC64@16777234$kCFBundleExecutableArchitectureX86_64@16777223$kCFCalendarComponentsWrap@1$kCFCalendarUnitDay@16$kCFCalendarUnitDayOfYear@65536$kCFCalendarUnitEra@2$kCFCalendarUnitHour@32$kCFCalendarUnitMinute@64$kCFCalendarUnitMonth@8$kCFCalendarUnitQuarter@2048$kCFCalendarUnitSecond@128$kCFCalendarUnitWeek@256$kCFCalendarUnitWeekOfMonth@4096$kCFCalendarUnitWeekOfYear@8192$kCFCalendarUnitWeekday@512$kCFCalendarUnitWeekdayOrdinal@1024$kCFCalendarUnitYear@4$kCFCalendarUnitYearForWeekOfYear@16384$kCFCharacterSetAlphaNumeric@10$kCFCharacterSetCapitalizedLetter@13$kCFCharacterSetControl@1$kCFCharacterSetDecimalDigit@4$kCFCharacterSetDecomposable@9$kCFCharacterSetIllegal@12$kCFCharacterSetLetter@5$kCFCharacterSetLowercaseLetter@6$kCFCharacterSetNewline@15$kCFCharacterSetNonBase@8$kCFCharacterSetPunctuation@11$kCFCharacterSetSymbol@14$kCFCharacterSetUppercaseLetter@7$kCFCharacterSetWhitespace@2$kCFCharacterSetWhitespaceAndNewline@3$kCFCompareAnchored@8$kCFCompareBackwards@4$kCFCompareCaseInsensitive@1$kCFCompareDiacriticInsensitive@128$kCFCompareEqualTo@0$kCFCompareForcedOrdering@512$kCFCompareGreaterThan@1$kCFCompareLessThan@-1$kCFCompareLocalized@32$kCFCompareNonliteral@16$kCFCompareNumerically@64$kCFCompareWidthInsensitive@256$kCFCoreFoundationVersionNumber10_10@1151.16$kCFCoreFoundationVersionNumber10_10_1@1151.16$kCFCoreFoundationVersionNumber10_10_2@1152$kCFCoreFoundationVersionNumber10_10_3@1153.18$kCFCoreFoundationVersionNumber10_10_4@1153.18$kCFCoreFoundationVersionNumber10_10_5@1153.18$kCFCoreFoundationVersionNumber10_10_Max@1199$kCFCoreFoundationVersionNumber10_11@1253$kCFCoreFoundationVersionNumber10_11_1@1255.1$kCFCoreFoundationVersionNumber10_11_2@1256.14$kCFCoreFoundationVersionNumber10_11_3@1256.14$kCFCoreFoundationVersionNumber10_11_4@1258.1$kCFCoreFoundationVersionNumber10_11_Max@1299$kCFDataSearchAnchored@2$kCFDataSearchBackwards@1$kCFDateFormatterFullStyle@4$kCFDateFormatterLongStyle@3$kCFDateFormatterMediumStyle@2$kCFDateFormatterNoStyle@0$kCFDateFormatterShortStyle@1$kCFFileDescriptorReadCallBack@1$kCFFileDescriptorWriteCallBack@2$kCFFileSecurityClearAccessControlList@32$kCFFileSecurityClearGroup@2$kCFFileSecurityClearGroupUUID@16$kCFFileSecurityClearMode@4$kCFFileSecurityClearOwner@1$kCFFileSecurityClearOwnerUUID@8$kCFGregorianAllUnits@16777215$kCFGregorianUnitsDays@4$kCFGregorianUnitsHours@8$kCFGregorianUnitsMinutes@16$kCFGregorianUnitsMonths@2$kCFGregorianUnitsSeconds@32$kCFGregorianUnitsYears@1$kCFISO8601DateFormatWithColonSeparatorInTime@512$kCFISO8601DateFormatWithColonSeparatorInTimeZone@1024$kCFISO8601DateFormatWithDashSeparatorInDate@256$kCFISO8601DateFormatWithDay@16$kCFISO8601DateFormatWithFractionalSeconds@2048$kCFISO8601DateFormatWithFullDate@275$kCFISO8601DateFormatWithFullTime@1632$kCFISO8601DateFormatWithInternetDateTime@1907$kCFISO8601DateFormatWithMonth@2$kCFISO8601DateFormatWithSpaceBetweenDateAndTime@128$kCFISO8601DateFormatWithTime@32$kCFISO8601DateFormatWithTimeZone@64$kCFISO8601DateFormatWithWeekOfYear@4$kCFISO8601DateFormatWithYear@1$kCFLocaleLanguageDirectionBottomToTop@4$kCFLocaleLanguageDirectionLeftToRight@1$kCFLocaleLanguageDirectionRightToLeft@2$kCFLocaleLanguageDirectionTopToBottom@3$kCFLocaleLanguageDirectionUnknown@0$kCFMessagePortBecameInvalidError@-5$kCFMessagePortIsInvalid@-3$kCFMessagePortReceiveTimeout@-2$kCFMessagePortSendTimeout@-1$kCFMessagePortSuccess@0$kCFMessagePortTransportError@-4$kCFNotFound@-1$kCFNotificationDeliverImmediately@1$kCFNotificationPostToAllSessions@2$kCFNumberCFIndexType@14$kCFNumberCGFloatType@16$kCFNumberCharType@7$kCFNumberDoubleType@13$kCFNumberFloat32Type@5$kCFNumberFloat64Type@6$kCFNumberFloatType@12$kCFNumberFormatterCurrencyAccountingStyle@10$kCFNumberFormatterCurrencyISOCodeStyle@8$kCFNumberFormatterCurrencyPluralStyle@9$kCFNumberFormatterCurrencyStyle@2$kCFNumberFormatterDecimalStyle@1$kCFNumberFormatterNoStyle@0$kCFNumberFormatterOrdinalStyle@6$kCFNumberFormatterPadAfterPrefix@1$kCFNumberFormatterPadAfterSuffix@3$kCFNumberFormatterPadBeforePrefix@0$kCFNumberFormatterPadBeforeSuffix@2$kCFNumberFormatterParseIntegersOnly@1$kCFNumberFormatterPercentStyle@3$kCFNumberFormatterRoundCeiling@0$kCFNumberFormatterRoundDown@2$kCFNumberFormatterRoundFloor@1$kCFNumberFormatterRoundHalfDown@5$kCFNumberFormatterRoundHalfEven@4$kCFNumberFormatterRoundHalfUp@6$kCFNumberFormatterRoundUp@3$kCFNumberFormatterScientificStyle@4$kCFNumberFormatterSpellOutStyle@5$kCFNumberIntType@9$kCFNumberLongLongType@11$kCFNumberLongType@10$kCFNumberMaxType@16$kCFNumberNSIntegerType@15$kCFNumberSInt16Type@2$kCFNumberSInt32Type@3$kCFNumberSInt64Type@4$kCFNumberSInt8Type@1$kCFNumberShortType@8$kCFPropertyListBinaryFormat_v1_0@200$kCFPropertyListImmutable@0$kCFPropertyListMutableContainers@1$kCFPropertyListMutableContainersAndLeaves@2$kCFPropertyListOpenStepFormat@1$kCFPropertyListReadCorruptError@3840$kCFPropertyListReadStreamError@3842$kCFPropertyListReadUnknownVersionError@3841$kCFPropertyListWriteStreamError@3851$kCFPropertyListXMLFormat_v1_0@100$kCFRunLoopAfterWaiting@64$kCFRunLoopAllActivities@268435455$kCFRunLoopBeforeSources@4$kCFRunLoopBeforeTimers@2$kCFRunLoopBeforeWaiting@32$kCFRunLoopEntry@1$kCFRunLoopExit@128$kCFRunLoopRunFinished@1$kCFRunLoopRunHandledSource@4$kCFRunLoopRunStopped@2$kCFRunLoopRunTimedOut@3$kCFSocketAcceptCallBack@2$kCFSocketAutomaticallyReenableAcceptCallBack@2$kCFSocketAutomaticallyReenableDataCallBack@3$kCFSocketAutomaticallyReenableReadCallBack@1$kCFSocketAutomaticallyReenableWriteCallBack@8$kCFSocketCloseOnInvalidate@128$kCFSocketConnectCallBack@4$kCFSocketDataCallBack@3$kCFSocketError@-1$kCFSocketLeaveErrors@64$kCFSocketNoCallBack@0$kCFSocketReadCallBack@1$kCFSocketSuccess@0$kCFSocketTimeout@-2$kCFSocketWriteCallBack@8$kCFStreamErrorDomainCustom@-1$kCFStreamErrorDomainMacOSStatus@2$kCFStreamErrorDomainPOSIX@1$kCFStreamEventCanAcceptBytes@4$kCFStreamEventEndEncountered@16$kCFStreamEventErrorOccurred@8$kCFStreamEventHasBytesAvailable@2$kCFStreamEventNone@0$kCFStreamEventOpenCompleted@1$kCFStreamStatusAtEnd@5$kCFStreamStatusClosed@6$kCFStreamStatusError@7$kCFStreamStatusNotOpen@0$kCFStreamStatusOpen@2$kCFStreamStatusOpening@1$kCFStreamStatusReading@3$kCFStreamStatusWriting@4$kCFStringEncodingANSEL@1537$kCFStringEncodingASCII@1536$kCFStringEncodingBig5@2563$kCFStringEncodingBig5_E@2569$kCFStringEncodingBig5_HKSCS_1999@2566$kCFStringEncodingCNS_11643_92_P1@1617$kCFStringEncodingCNS_11643_92_P2@1618$kCFStringEncodingCNS_11643_92_P3@1619$kCFStringEncodingDOSArabic@1049$kCFStringEncodingDOSBalticRim@1030$kCFStringEncodingDOSCanadianFrench@1048$kCFStringEncodingDOSChineseSimplif@1057$kCFStringEncodingDOSChineseTrad@1059$kCFStringEncodingDOSCyrillic@1043$kCFStringEncodingDOSGreek@1029$kCFStringEncodingDOSGreek1@1041$kCFStringEncodingDOSGreek2@1052$kCFStringEncodingDOSHebrew@1047$kCFStringEncodingDOSIcelandic@1046$kCFStringEncodingDOSJapanese@1056$kCFStringEncodingDOSKorean@1058$kCFStringEncodingDOSLatin1@1040$kCFStringEncodingDOSLatin2@1042$kCFStringEncodingDOSLatinUS@1024$kCFStringEncodingDOSNordic@1050$kCFStringEncodingDOSPortuguese@1045$kCFStringEncodingDOSRussian@1051$kCFStringEncodingDOSThai@1053$kCFStringEncodingDOSTurkish@1044$kCFStringEncodingEBCDIC_CP037@3074$kCFStringEncodingEBCDIC_US@3073$kCFStringEncodingEUC_CN@2352$kCFStringEncodingEUC_JP@2336$kCFStringEncodingEUC_KR@2368$kCFStringEncodingEUC_TW@2353$kCFStringEncodingGBK_95@1585$kCFStringEncodingGB_18030_2000@1586$kCFStringEncodingGB_2312_80@1584$kCFStringEncodingHZ_GB_2312@2565$kCFStringEncodingISOLatin1@513$kCFStringEncodingISOLatin10@528$kCFStringEncodingISOLatin2@514$kCFStringEncodingISOLatin3@515$kCFStringEncodingISOLatin4@516$kCFStringEncodingISOLatin5@521$kCFStringEncodingISOLatin6@522$kCFStringEncodingISOLatin7@525$kCFStringEncodingISOLatin8@526$kCFStringEncodingISOLatin9@527$kCFStringEncodingISOLatinArabic@518$kCFStringEncodingISOLatinCyrillic@517$kCFStringEncodingISOLatinGreek@519$kCFStringEncodingISOLatinHebrew@520$kCFStringEncodingISOLatinThai@523$kCFStringEncodingISO_2022_CN@2096$kCFStringEncodingISO_2022_CN_EXT@2097$kCFStringEncodingISO_2022_JP@2080$kCFStringEncodingISO_2022_JP_1@2082$kCFStringEncodingISO_2022_JP_2@2081$kCFStringEncodingISO_2022_JP_3@2083$kCFStringEncodingISO_2022_KR@2112$kCFStringEncodingInvalidId@4294967295$kCFStringEncodingJIS_C6226_78@1572$kCFStringEncodingJIS_X0201_76@1568$kCFStringEncodingJIS_X0208_83@1569$kCFStringEncodingJIS_X0208_90@1570$kCFStringEncodingJIS_X0212_90@1571$kCFStringEncodingKOI8_R@2562$kCFStringEncodingKOI8_U@2568$kCFStringEncodingKSC_5601_87@1600$kCFStringEncodingKSC_5601_92_Johab@1601$kCFStringEncodingMacArabic@4$kCFStringEncodingMacArmenian@24$kCFStringEncodingMacBengali@13$kCFStringEncodingMacBurmese@19$kCFStringEncodingMacCeltic@39$kCFStringEncodingMacCentralEurRoman@29$kCFStringEncodingMacChineseSimp@25$kCFStringEncodingMacChineseTrad@2$kCFStringEncodingMacCroatian@36$kCFStringEncodingMacCyrillic@7$kCFStringEncodingMacDevanagari@9$kCFStringEncodingMacDingbats@34$kCFStringEncodingMacEthiopic@28$kCFStringEncodingMacExtArabic@31$kCFStringEncodingMacFarsi@140$kCFStringEncodingMacGaelic@40$kCFStringEncodingMacGeorgian@23$kCFStringEncodingMacGreek@6$kCFStringEncodingMacGujarati@11$kCFStringEncodingMacGurmukhi@10$kCFStringEncodingMacHFS@255$kCFStringEncodingMacHebrew@5$kCFStringEncodingMacIcelandic@37$kCFStringEncodingMacInuit@236$kCFStringEncodingMacJapanese@1$kCFStringEncodingMacKannada@16$kCFStringEncodingMacKhmer@20$kCFStringEncodingMacKorean@3$kCFStringEncodingMacLaotian@22$kCFStringEncodingMacMalayalam@17$kCFStringEncodingMacMongolian@27$kCFStringEncodingMacOriya@12$kCFStringEncodingMacRoman@0$kCFStringEncodingMacRomanLatin1@2564$kCFStringEncodingMacRomanian@38$kCFStringEncodingMacSinhalese@18$kCFStringEncodingMacSymbol@33$kCFStringEncodingMacTamil@14$kCFStringEncodingMacTelugu@15$kCFStringEncodingMacThai@21$kCFStringEncodingMacTibetan@26$kCFStringEncodingMacTurkish@35$kCFStringEncodingMacUkrainian@152$kCFStringEncodingMacVT100@252$kCFStringEncodingMacVietnamese@30$kCFStringEncodingNextStepJapanese@2818$kCFStringEncodingNextStepLatin@2817$kCFStringEncodingNonLossyASCII@3071$kCFStringEncodingShiftJIS@2561$kCFStringEncodingShiftJIS_X0213@1576$kCFStringEncodingShiftJIS_X0213_00@1576$kCFStringEncodingShiftJIS_X0213_MenKuTen@1577$kCFStringEncodingUTF16@256$kCFStringEncodingUTF16BE@268435712$kCFStringEncodingUTF16LE@335544576$kCFStringEncodingUTF32@201326848$kCFStringEncodingUTF32BE@402653440$kCFStringEncodingUTF32LE@469762304$kCFStringEncodingUTF7@67109120$kCFStringEncodingUTF7_IMAP@2576$kCFStringEncodingUTF8@134217984$kCFStringEncodingUnicode@256$kCFStringEncodingVISCII@2567$kCFStringEncodingWindowsArabic@1286$kCFStringEncodingWindowsBalticRim@1287$kCFStringEncodingWindowsCyrillic@1282$kCFStringEncodingWindowsGreek@1283$kCFStringEncodingWindowsHebrew@1285$kCFStringEncodingWindowsKoreanJohab@1296$kCFStringEncodingWindowsLatin1@1280$kCFStringEncodingWindowsLatin2@1281$kCFStringEncodingWindowsLatin5@1284$kCFStringEncodingWindowsVietnamese@1288$kCFStringNormalizationFormC@2$kCFStringNormalizationFormD@0$kCFStringNormalizationFormKC@3$kCFStringNormalizationFormKD@1$kCFStringTokenizerAttributeLanguage@131072$kCFStringTokenizerAttributeLatinTranscription@65536$kCFStringTokenizerTokenHasDerivedSubTokensMask@4$kCFStringTokenizerTokenHasHasNumbersMask@8$kCFStringTokenizerTokenHasNonLettersMask@16$kCFStringTokenizerTokenHasSubTokensMask@2$kCFStringTokenizerTokenIsCJWordMask@32$kCFStringTokenizerTokenNone@0$kCFStringTokenizerTokenNormal@1$kCFStringTokenizerUnitLineBreak@3$kCFStringTokenizerUnitParagraph@2$kCFStringTokenizerUnitSentence@1$kCFStringTokenizerUnitWord@0$kCFStringTokenizerUnitWordBoundary@4$kCFTimeZoneNameStyleDaylightSaving@2$kCFTimeZoneNameStyleGeneric@4$kCFTimeZoneNameStyleShortDaylightSaving@3$kCFTimeZoneNameStyleShortGeneric@5$kCFTimeZoneNameStyleShortStandard@1$kCFTimeZoneNameStyleStandard@0$kCFURLBookmarkCreationMinimalBookmarkMask@512$kCFURLBookmarkCreationPreferFileIDResolutionMask@256$kCFURLBookmarkCreationSecurityScopeAllowOnlyReadAccess@4096$kCFURLBookmarkCreationSuitableForBookmarkFile@1024$kCFURLBookmarkCreationWithSecurityScope@2048$kCFURLBookmarkCreationWithoutImplicitSecurityScope@536870912$kCFURLBookmarkResolutionWithSecurityScope@1024$kCFURLBookmarkResolutionWithoutImplicitStartAccessing@32768$kCFURLBookmarkResolutionWithoutMountingMask@512$kCFURLBookmarkResolutionWithoutUIMask@256$kCFURLComponentFragment@12$kCFURLComponentHost@8$kCFURLComponentNetLocation@2$kCFURLComponentParameterString@10$kCFURLComponentPassword@6$kCFURLComponentPath@3$kCFURLComponentPort@9$kCFURLComponentQuery@11$kCFURLComponentResourceSpecifier@4$kCFURLComponentScheme@1$kCFURLComponentUser@5$kCFURLComponentUserInfo@7$kCFURLEnumeratorDefaultBehavior@0$kCFURLEnumeratorDescendRecursively@1$kCFURLEnumeratorDirectoryPostOrderSuccess@4$kCFURLEnumeratorEnd@2$kCFURLEnumeratorError@3$kCFURLEnumeratorGenerateFileReferenceURLs@4$kCFURLEnumeratorGenerateRelativePathURLs@64$kCFURLEnumeratorIncludeDirectoriesPostOrder@32$kCFURLEnumeratorIncludeDirectoriesPreOrder@16$kCFURLEnumeratorSkipInvisibles@2$kCFURLEnumeratorSkipPackageContents@8$kCFURLEnumeratorSuccess@1$kCFURLHFSPathStyle@1$kCFURLImproperArgumentsError@-15$kCFURLPOSIXPathStyle@0$kCFURLPropertyKeyUnavailableError@-17$kCFURLRemoteHostUnavailableError@-14$kCFURLResourceAccessViolationError@-13$kCFURLResourceNotFoundError@-12$kCFURLTimeoutError@-18$kCFURLUnknownError@-10$kCFURLUnknownPropertyKeyError@-16$kCFURLUnknownSchemeError@-11$kCFURLWindowsPathStyle@2$kCFUserNotificationAlternateResponse@1$kCFUserNotificationCancelResponse@3$kCFUserNotificationCautionAlertLevel@2$kCFUserNotificationDefaultResponse@0$kCFUserNotificationNoDefaultButtonFlag@32$kCFUserNotificationNoteAlertLevel@1$kCFUserNotificationOtherResponse@2$kCFUserNotificationPlainAlertLevel@3$kCFUserNotificationStopAlertLevel@0$kCFUserNotificationUseRadioButtonsFlag@64$kCFXMLEntityTypeCharacter@4$kCFXMLEntityTypeParameter@0$kCFXMLEntityTypeParsedExternal@2$kCFXMLEntityTypeParsedInternal@1$kCFXMLEntityTypeUnparsed@3$kCFXMLErrorElementlessDocument@11$kCFXMLErrorEncodingConversionFailure@3$kCFXMLErrorMalformedCDSect@7$kCFXMLErrorMalformedCharacterReference@13$kCFXMLErrorMalformedCloseTag@8$kCFXMLErrorMalformedComment@12$kCFXMLErrorMalformedDTD@5$kCFXMLErrorMalformedDocument@10$kCFXMLErrorMalformedName@6$kCFXMLErrorMalformedParsedCharacterData@14$kCFXMLErrorMalformedProcessingInstruction@4$kCFXMLErrorMalformedStartTag@9$kCFXMLErrorNoData@15$kCFXMLErrorUnexpectedEOF@1$kCFXMLErrorUnknownEncoding@2$kCFXMLNodeCurrentVersion@1$kCFXMLNodeTypeAttribute@3$kCFXMLNodeTypeAttributeListDeclaration@15$kCFXMLNodeTypeCDATASection@7$kCFXMLNodeTypeComment@5$kCFXMLNodeTypeDocument@1$kCFXMLNodeTypeDocumentFragment@8$kCFXMLNodeTypeDocumentType@11$kCFXMLNodeTypeElement@2$kCFXMLNodeTypeElementTypeDeclaration@14$kCFXMLNodeTypeEntity@9$kCFXMLNodeTypeEntityReference@10$kCFXMLNodeTypeNotation@13$kCFXMLNodeTypeProcessingInstruction@4$kCFXMLNodeTypeText@6$kCFXMLNodeTypeWhitespace@12$kCFXMLParserAddImpliedAttributes@32$kCFXMLParserAllOptions@16777215$kCFXMLParserNoOptions@0$kCFXMLParserReplacePhysicalEntities@4$kCFXMLParserResolveExternalEntities@16$kCFXMLParserSkipMetaData@2$kCFXMLParserSkipWhitespace@8$kCFXMLParserValidateDocument@1$kCFXMLStatusParseInProgress@-1$kCFXMLStatusParseNotBegun@-2$kCFXMLStatusParseSuccessful@0$"""
misc.update(
    {
        "CFCharacterSetPredefinedSet": NewType("CFCharacterSetPredefinedSet", int),
        "CFStringTokenizerTokenType": NewType("CFStringTokenizerTokenType", int),
        "CFComparisonResult": NewType("CFComparisonResult", int),
        "CFStringCompareFlags": NewType("CFStringCompareFlags", int),
        "CFPropertyListMutabilityOptions": NewType(
            "CFPropertyListMutabilityOptions", int
        ),
        "CFNotificationSuspensionBehavior": NewType(
            "CFNotificationSuspensionBehavior", int
        ),
        "CFURLComponentType": NewType("CFURLComponentType", int),
        "CFStreamStatus": NewType("CFStreamStatus", int),
        "CFFileSecurityClearOptions": NewType("CFFileSecurityClearOptions", int),
        "CFNumberFormatterStyle": NewType("CFNumberFormatterStyle", int),
        "CFStringNormalizationForm": NewType("CFStringNormalizationForm", int),
        "CFXMLParserOptions": NewType("CFXMLParserOptions", int),
        "CFRunLoopRunResult": NewType("CFRunLoopRunResult", int),
        "CFURLBookmarkCreationOptions": NewType("CFURLBookmarkCreationOptions", int),
        "__CFByteOrder": NewType("__CFByteOrder", int),
        "CFStreamEventType": NewType("CFStreamEventType", int),
        "CFDateFormatterStyle": NewType("CFDateFormatterStyle", int),
        "CFSocketError": NewType("CFSocketError", int),
        "CFXMLNodeTypeCode": NewType("CFXMLNodeTypeCode", int),
        "CFXMLParserStatusCode": NewType("CFXMLParserStatusCode", int),
        "CFRunLoopActivity": NewType("CFRunLoopActivity", int),
        "CFNumberFormatterRoundingMode": NewType("CFNumberFormatterRoundingMode", int),
        "CFURLBookmarkResolutionOptions": NewType(
            "CFURLBookmarkResolutionOptions", int
        ),
        "CFDataSearchFlags": NewType("CFDataSearchFlags", int),
        "CFISO8601DateFormatOptions": NewType("CFISO8601DateFormatOptions", int),
        "CFNumberFormatterOptionFlags": NewType("CFNumberFormatterOptionFlags", int),
        "CFStreamErrorDomain": NewType("CFStreamErrorDomain", int),
        "CFTimeZoneNameStyle": NewType("CFTimeZoneNameStyle", int),
        "CFSocketCallBackType": NewType("CFSocketCallBackType", int),
        "CFURLEnumeratorOptions": NewType("CFURLEnumeratorOptions", int),
        "CFXMLEntityTypeCode": NewType("CFXMLEntityTypeCode", int),
        "CFStringBuiltInEncodings": NewType("CFStringBuiltInEncodings", int),
        "CFCalendarUnit": NewType("CFCalendarUnit", int),
        "CFLocaleLanguageDirection": NewType("CFLocaleLanguageDirection", int),
        "CFNumberType": NewType("CFNumberType", int),
        "CFURLPathStyle": NewType("CFURLPathStyle", int),
        "CFURLEnumeratorResult": NewType("CFURLEnumeratorResult", int),
        "CFGregorianUnitFlags": NewType("CFGregorianUnitFlags", int),
        "CFURLError": NewType("CFURLError", int),
        "CFStringEncodings": NewType("CFStringEncodings", int),
        "CFNumberFormatterPadPosition": NewType("CFNumberFormatterPadPosition", int),
        "CGRectEdge": NewType("CGRectEdge", int),
        "CFPropertyListFormat": NewType("CFPropertyListFormat", int),
    }
)
misc.update({})
misc.update(
    {
        "kCFCoreFoundationVersionNumber10_7_1": 635.0,
        "kCFCoreFoundationVersionNumber10_4_4_Intel": 368.26,
        "kCFCoreFoundationVersionNumber10_11_4": 1258.1,
        "kCFCoreFoundationVersionNumber10_11_3": 1256.14,
        "kCFCoreFoundationVersionNumber10_11_2": 1256.14,
        "kCFCoreFoundationVersionNumber10_11_1": 1255.1,
        "kCFCoreFoundationVersionNumber10_10_4": 1153.18,
        "kCFCoreFoundationVersionNumber10_10_5": 1153.18,
        "kCFCoreFoundationVersionNumber10_10_3": 1153.18,
        "kCFCoreFoundationVersionNumber10_10_1": 1151.16,
        "kCFCoreFoundationVersionNumber10_8_2": 744.12,
        "kCFCoreFoundationVersionNumber10_8_3": 744.18,
        "kCFCoreFoundationVersionNumber10_8_1": 744.0,
        "kCFCoreFoundationVersionNumber10_5_1": 476.0,
        "kCFCoreFoundationVersionNumber10_8_4": 744.19,
        "kCFCoreFoundationVersionNumber10_9_2": 855.14,
        "kCFCoreFoundationVersionNumber10_9_1": 855.11,
        "kCFCoreFoundationVersionNumber10_4_7": 368.27,
        "kCFCoreFoundationVersionNumber10_4_2": 368.11,
        "kCFCoreFoundationVersionNumber10_4_3": 368.18,
        "kCFCoreFoundationVersionNumber10_4_1": 368.1,
        "kCFCoreFoundationVersionNumber10_5_7": 476.18,
        "kCFCoreFoundationVersionNumber10_5_6": 476.17,
        "kCFCoreFoundationVersionNumber10_5_5": 476.15,
        "kCFCoreFoundationVersionNumber10_5_4": 476.14,
        "kCFCoreFoundationVersionNumber10_5_3": 476.13,
        "kCFCoreFoundationVersionNumber10_5_2": 476.1,
        "kCFCoreFoundationVersionNumber10_4_8": 368.27,
        "kCFCoreFoundationVersionNumber10_4_9": 368.28,
        "kCFCoreFoundationVersionNumber10_2_4": 263.3,
        "kCFCoreFoundationVersionNumber10_2_5": 263.5,
        "kCFCoreFoundationVersionNumber10_2_6": 263.5,
        "kCFCoreFoundationVersionNumber10_2_7": 263.5,
        "kCFCoreFoundationVersionNumber10_3_9": 299.35,
        "kCFCoreFoundationVersionNumber10_2_1": 263.1,
        "kCFCoreFoundationVersionNumber10_2_2": 263.1,
        "kCFCoreFoundationVersionNumber10_2_3": 263.3,
        "kCFCoreFoundationVersionNumber10_3_5": 299.31,
        "kCFCoreFoundationVersionNumber10_3_4": 299.31,
        "kCFCoreFoundationVersionNumber10_3_7": 299.33,
        "kCFCoreFoundationVersionNumber10_3_6": 299.32,
        "kCFCoreFoundationVersionNumber10_3_1": 299.0,
        "kCFCoreFoundationVersionNumber10_3_3": 299.3,
        "kCFCoreFoundationVersionNumber10_3_2": 299.0,
        "kCFCoreFoundationVersionNumber10_1_3": 227.2,
        "kCFCoreFoundationVersionNumber10_1_2": 227.2,
        "kCFCoreFoundationVersionNumber10_1_1": 226.0,
        "kCFCoreFoundationVersionNumber10_1_4": 227.3,
        "kCFCoreFoundationVersionNumber10_4_6_PowerPC": 368.25,
        "kCFCoreFoundationVersionNumber10_6_2": 550.13,
        "kCFCoreFoundationVersionNumber10_6_3": 550.19,
        "kCFCoreFoundationVersionNumber10_6_4": 550.29,
        "kCFCoreFoundationVersionNumber10_6_5": 550.42,
        "kCFCoreFoundationVersionNumber10_6_6": 550.42,
        "kCFCoreFoundationVersionNumber10_4_4_PowerPC": 368.25,
        "kCFCoreFoundationVersionNumber10_6_8": 550.43,
        "kCFCoreFoundationVersionNumber10_0_3": 196.5,
        "kCFCoreFoundationVersionNumber10_7_3": 635.19,
        "kCFCoreFoundationVersionNumber10_7_2": 635.15,
        "kCFCoreFoundationVersionNumber10_4_10": 368.28,
        "kCFCoreFoundationVersionNumber10_7_4": 635.21,
        "kCFCoreFoundationVersionNumber10_4_5_PowerPC": 368.25,
        "kCFCoreFoundationVersionNumber10_3_8": 299.33,
        "kCFCoreFoundationVersionNumber10_10": 1151.16,
        "kCFCoreFoundationVersionNumber10_4_5_Intel": 368.26,
        "kCFCoreFoundationVersionNumber10_2_8": 263.5,
        "kCFCoreFoundationVersionNumber10_5_8": 476.19,
        "kCFCoreFoundationVersionNumber10_4_11": 368.31,
        "kCFCoreFoundationVersionNumber10_6_1": 550.0,
        "kCFCoreFoundationVersionNumber10_6_7": 550.42,
        "kCFCoreFoundationVersionNumber10_8": 744.0,
        "kCFCoreFoundationVersionNumber10_5": 476.0,
        "kCFCoreFoundationVersionNumber10_4": 368.0,
        "kCFCoreFoundationVersionNumber10_7": 635.0,
        "kCFCoreFoundationVersionNumber10_6": 550.0,
        "kCFCoreFoundationVersionNumber10_1": 226.0,
        "kCFCoreFoundationVersionNumber10_0": 196.4,
        "kCFCoreFoundationVersionNumber10_3": 299.0,
        "kCFCoreFoundationVersionNumber10_2": 263.0,
        "kCFCoreFoundationVersionNumber10_7_5": 635.21,
        "kCFCoreFoundationVersionNumber10_9": 855.11,
        "kCFCoreFoundationVersionNumber10_4_6_Intel": 368.26,
    }
)
functions = {
    "CFURLCreateByResolvingBookmarkData": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFData=}Q^{__CFURL=}^{__CFArray=}^Z^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {"type_modifier": "o"},
                6: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            },
        },
    ),
    "CFConvertDoubleSwappedToHost": (b"d{CFSwappedFloat64=Q}",),
    "CFURLCreateCopyAppendingPathComponent": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFURL=}^{__CFString=}Z",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRangeMake": (b"{CFRange=qq}qq",),
    "CFBitVectorGetCount": (b"q^{__CFBitVector=}",),
    "CFDictionaryContainsKey": (b"Z^{__CFDictionary=}@",),
    "CFPreferencesCopyValue": (
        b"@^{__CFString=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFGetAllocator": (b"^{__CFAllocator=}@",),
    "CFSetCreateMutable": (
        b"^{__CFSet=}^{__CFAllocator=}q^{CFSetCallBacks=q^?^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFErrorGetCode": (b"q^{__CFError=}",),
    "CFStringGetFileSystemRepresentation": (
        b"Z^{__CFString=}^tq",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "o"}}},
    ),
    "CFLocaleGetTypeID": (b"Q",),
    "CFUUIDGetUUIDBytes": (b"{CFUUIDBytes=CCCCCCCCCCCCCCCC}^{__CFUUID=}",),
    "CFDateFormatterCreateDateFormatFromTemplate": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}Q^{__CFLocale=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFTreeInsertSibling": (b"v^{__CFTree=}^{__CFTree=}",),
    "CFSocketConnectToAddress": (b"q^{__CFSocket=}^{__CFData=}d",),
    "CFWriteStreamScheduleWithRunLoop": (
        b"v^{__CFWriteStream=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "CFDateFormatterCreateStringWithAbsoluteTime": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFDateFormatter=}d",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFReadStreamScheduleWithRunLoop": (
        b"v^{__CFReadStream=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "CFArrayAppendValue": (b"v^{__CFArray=}@",),
    "CFSetRemoveValue": (b"v^{__CFSet=}@",),
    "CFBundleCopyPrivateFrameworksURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorCreateMutable": (
        b"^{__CFBitVector=}^{__CFAllocator=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFLocaleCreateCanonicalLocaleIdentifierFromString": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringTokenizerCopyBestStringLanguage": (
        b"^{__CFString=}^{__CFString=}{CFRange=qq}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUUIDCreate": (
        b"^{__CFUUID=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCalendarGetRangeOfUnit": (b"{CFRange=qq}^{__CFCalendar=}QQd",),
    "CFStringFindWithOptionsAndLocale": (
        b"Z^{__CFString=}^{__CFString=}{CFRange=qq}Q^{__CFLocale=}^{CFRange=qq}",
        "",
        {"arguments": {5: {"type_modifier": "o"}}},
    ),
    "CFURLSetResourcePropertyForKey": (
        b"Z^{__CFURL=}^{__CFString=}@^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFFileSecurityCopyOwnerUUID": (
        b"Z^{__CFFileSecurity=}^^{__CFUUID=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CFCalendarAddComponents": (b"Z^{__CFCalendar=}^dQ^c", "", {"variadic": True}),
    "CFLocaleCopyCommonISOCurrencyCodes": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCalendarGetOrdinalityOfUnit": (b"q^{__CFCalendar=}QQd",),
    "CFPreferencesRemoveSuitePreferencesFromApp": (b"v^{__CFString=}^{__CFString=}",),
    "CFCalendarGetMinimumDaysInFirstWeek": (b"q^{__CFCalendar=}",),
    "CFURLCreateWithFileSystemPathRelativeToBase": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFString=}qZ^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBinaryHeapGetCountOfValue": (b"q^{__CFBinaryHeap=}@",),
    "CFStringIsSurrogateLowCharacter": (b"ZT",),
    "CFStringTrim": (b"v^{__CFString=}^{__CFString=}",),
    "CFXMLNodeGetTypeID": (b"Q",),
    "CFStringSetExternalCharactersNoCopy": (
        b"v^{__CFString=}^Tqq",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 3, "type_modifier": "n"}},
        },
    ),
    "CFLocaleGetSystem": (b"^{__CFLocale=}",),
    "CFDataGetLength": (b"q^{__CFData=}",),
    "CFWriteStreamWrite": (
        b"q^{__CFWriteStream=}^vq",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CFBundleGetVersionNumber": (b"I^{__CFBundle=}",),
    "CFGetRetainCount": (b"q@",),
    "CFRunLoopObserverGetContext": (
        b"v^{__CFRunLoopObserver=}^{CFRunLoopObserverContext=q^v^?^?^?}",
    ),
    "CFDataCreateWithBytesNoCopy": (
        b"^{__CFData=}^{__CFAllocator=}^vq^{__CFAllocator=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFURLEnumeratorGetNextURL": (
        b"q^{__CFURLEnumerator=}^^{__CFURL=}^^{__CFError=}",
        "",
        {
            "arguments": {
                1: {"type_modifier": "o"},
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            }
        },
    ),
    "CFRunLoopTimerGetTypeID": (b"Q",),
    "CFStringConvertNSStringEncodingToEncoding": (b"IQ",),
    "CFURLCreateBookmarkDataFromFile": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFURL=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "CFBundleCopyResourceURLForLocalization": (
        b"^{__CFURL=}^{__CFBundle=}^{__CFString=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetMaximumSizeForEncoding": (b"qqI",),
    "CFStringTransform": (
        b"Z^{__CFString=}^{CFRange=qq}^{__CFString=}Z",
        "",
        {"arguments": {1: {"type_modifier": "N"}}},
    ),
    "CFURLStopAccessingSecurityScopedResource": (b"v^{__CFURL=}",),
    "CFDataCreateMutableCopy": (
        b"^{__CFData=}^{__CFAllocator=}q^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFMachPortGetContext": (b"v^{__CFMachPort=}^{CFMachPortContext=q^v^?^?^?}",),
    "CFDateFormatterGetDateStyle": (b"q^{__CFDateFormatter=}",),
    "CFStringGetHyphenationLocationBeforeIndex": (
        b"q^{__CFString=}q{CFRange=qq}Q^{__CFLocale=}^I",
        "",
        {"arguments": {5: {"type_modifier": "o"}}},
    ),
    "CFRunLoopIsWaiting": (b"Z^{__CFRunLoop=}",),
    "CFAttributedStringReplaceString": (
        b"v^{__CFAttributedString=}{CFRange=qq}^{__CFString=}",
    ),
    "CFSocketCreateWithNative": (
        b"^{__CFSocket=}^{__CFAllocator=}iQ^?^{CFSocketContext=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFSocket=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^{__CFData=}"},
                            3: {"type": b"^v"},
                            4: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFMessagePortCreateLocal": (
        b"^{__CFMessagePort=}^{__CFAllocator=}^{__CFString=}^?^{CFMessagePortContext=q^v^?^?^?}^Z",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"^{__CFData=}", "already_cfretained": True},
                        "arguments": {
                            0: {"type": b"^{__CFMessagePort=}"},
                            1: {"type": b"i"},
                            2: {"type": b"^{__CFData=}"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFTimeZoneCopyDefault": (
        b"^{__CFTimeZone=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFArrayGetValueAtIndex": (b"@^{__CFArray=}q",),
    "CFErrorCopyFailureReason": (
        b"^{__CFString=}^{__CFError=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBinaryHeapContainsValue": (b"Z^{__CFBinaryHeap=}@",),
    "CFNumberFormatterGetStyle": (b"q^{__CFNumberFormatter=}",),
    "CFLocaleCopyPreferredLanguages": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBagCreateCopy": (
        b"^{__CFBag=}^{__CFAllocator=}^{__CFBag=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNotificationCenterGetDistributedCenter": (b"^{__CFNotificationCenter=}",),
    "CFXMLTreeGetNode": (b"^{__CFXMLNode=}^{__CFTree=}",),
    "CFDateCreate": (
        b"^{__CFDate=}^{__CFAllocator=}d",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFErrorCopyDescription": (
        b"^{__CFString=}^{__CFError=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetGetTypeID": (b"Q",),
    "CFWriteStreamCopyProperty": (
        b"@^{__CFWriteStream=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringGetLength": (b"q^{__CFAttributedString=}",),
    "CFStringGetCStringPtr": (
        b"^t@I",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "CFFileDescriptorEnableCallBacks": (b"v^{__CFFileDescriptor=}Q",),
    "CFURLGetString": (b"^{__CFString=}^{__CFURL=}",),
    "CFReadStreamSetProperty": (b"Z^{__CFReadStream=}^{__CFString=}@",),
    "CFFileDescriptorInvalidate": (b"v^{__CFFileDescriptor=}",),
    "CFBagGetCountOfValue": (b"q^{__CFBag=}@",),
    "CFAbsoluteTimeGetCurrent": (b"d",),
    "CFLocaleCopyISOCurrencyCodes": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCalendarGetTypeID": (b"Q",),
    "CFBundleCopySharedFrameworksURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringCreateWithSubstring": (
        b"^{__CFAttributedString=}^{__CFAllocator=}^{__CFAttributedString=}{CFRange=qq}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringAppend": (b"v^{__CFString=}^{__CFString=}",),
    "CFRelease": (b"v@",),
    "CFAllocatorGetDefault": (b"^{__CFAllocator=}",),
    "CFStringTokenizerAdvanceToNextToken": (b"Q^{__CFStringTokenizer=}",),
    "CFAttributedStringGetAttributeAndLongestEffectiveRange": (
        b"@^{__CFAttributedString=}q^{__CFString=}{CFRange=qq}^{CFRange=qq}",
        "",
        {"arguments": {4: {"type_modifier": "o"}}},
    ),
    "CFFileDescriptorGetContext": (
        b"v^{__CFFileDescriptor=}^{CFFileDescriptorContext=q^v^?^?^?}",
    ),
    "CFUserNotificationPopUpSelection": (b"Qq",),
    "CFStringConvertIANACharSetNameToEncoding": (b"I^{__CFString=}",),
    "CFDateFormatterGetTimeStyle": (b"q^{__CFDateFormatter=}",),
    "CFSocketSetSocketFlags": (b"v^{__CFSocket=}Q",),
    "CFXMLCreateStringByUnescapingEntities": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateWithSubstring": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}{CFRange=qq}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFWriteStreamSetDispatchQueue": (b"v^{__CFWriteStream=}@",),
    "CFStringCreateMutableCopy": (
        b"^{__CFString=}^{__CFAllocator=}q^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleCopyExecutableArchitectures": (
        b"^{__CFArray=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDictionaryCreateCopy": (
        b"^{__CFDictionary=}^{__CFAllocator=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopPerformBlock": (
        b"v^{__CFRunLoop=}@@?",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    ),
    "CFStringPad": (b"v^{__CFString=}^{__CFString=}qq",),
    "CFLocaleGetValue": (b"@^{__CFLocale=}^{__CFString=}",),
    "CFLocaleCopyISOLanguageCodes": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFSocketSendData": (b"q^{__CFSocket=}^{__CFData=}^{__CFData=}d",),
    "CFDataIncreaseLength": (b"v^{__CFData=}q",),
    "CFBagGetValueIfPresent": (
        b"Z^{__CFBag=}@^@",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFBooleanGetTypeID": (b"Q",),
    "CFBundleCopyAuxiliaryExecutableURL": (
        b"^{__CFURL=}^{__CFBundle=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLEnumeratorCreateForMountedVolumes": (
        b"^{__CFURLEnumerator=}^{__CFAllocator=}Q^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFFileSecurityGetMode": (
        b"Z^{__CFFileSecurity=}^S",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CFReadStreamSetClient": (
        b"Z^{__CFReadStream=}Q^?^{CFStreamClientContext=q^v^?^?^?}",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFReadStream=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CFStringConvertEncodingToWindowsCodepage": (b"II",),
    "CFMachPortGetInvalidationCallBack": (b"^?^{__CFMachPort=}",),
    "CFURLCopyFileSystemPath": (
        b"^{__CFString=}^{__CFURL=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopSourceSignal": (b"v^{__CFRunLoopSource=}",),
    "CFBundleCopyInfoDictionaryInDirectory": (
        b"^{__CFDictionary=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUserNotificationDisplayAlert": (
        b"idQ^{__CFURL=}^{__CFURL=}^{__CFURL=}^{__CFString=}^{__CFString=}^{__CFString=}^{__CFString=}^{__CFString=}^Q",
        "",
        {"arguments": {10: {"type_modifier": "o"}}},
    ),
    "CFURLClearResourcePropertyCacheForKey": (b"v^{__CFURL=}^{__CFString=}",),
    "CFBinaryHeapGetMinimum": (b"@^{__CFBinaryHeap=}",),
    "CFNotificationCenterAddObserver": (
        b"v^{__CFNotificationCenter=}@^?@@q",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                            4: {"type": b"@"},
                        },
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "CFCalendarCopyTimeZone": (
        b"^{__CFTimeZone=}^{__CFCalendar=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringGetTypeID": (b"Q",),
    "CFPropertyListWriteToStream": (
        b"q@^{__CFWriteStream=}q^^{__CFString=}",
        "",
        {"arguments": {3: {"type_modifier": "o"}}},
    ),
    "CFBagAddValue": (b"v^{__CFBag=}@",),
    "CFCharacterSetCreateMutable": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFTreeGetParent": (b"^{__CFTree=}^{__CFTree=}",),
    "CFCalendarGetComponentDifference": (
        b"Z^{__CFCalendar=}ddQ^c",
        "",
        {"variadic": True},
    ),
    "CFURLGetByteRangeForComponent": (
        b"{CFRange=qq}^{__CFURL=}q^{CFRange=qq}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFRunLoopRunInMode": (b"i^{__CFString=}dZ",),
    "CFBundleCopyExecutableURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringReplace": (b"v^{__CFString=}{CFRange=qq}^{__CFString=}",),
    "CFSocketGetNative": (b"i^{__CFSocket=}",),
    "CFConvertFloatSwappedToHost": (b"f{CFSwappedFloat32=I}",),
    "CFBundleOpenBundleResourceMap": (b"i^{__CFBundle=}",),
    "CFDataFind": (b"{CFRange=qq}^{__CFData=}^{__CFData=}{CFRange=qq}Q",),
    "CFMachPortCreate": (
        b"^{__CFMachPort=}^{__CFAllocator=}^?^{CFMachPortContext=q^v^?^?^?}^Z",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFMachPort=}"},
                            1: {"type": b"^v"},
                            2: {"type": b"q"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFAttributedStringReplaceAttributedString": (
        b"v^{__CFAttributedString=}{CFRange=qq}^{__CFAttributedString=}",
    ),
    "CFTimeZoneCreateWithName": (
        b"^{__CFTimeZone=}^{__CFAllocator=}^{__CFString=}Z",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleGetPackageInfoInDirectory": (
        b"Z^{__CFURL=}^I^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CFURLCreateData": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFURL=}IZ",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNumberFormatterCreateStringWithNumber": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFNumberFormatter=}^{__CFNumber=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCalendarGetMaximumRangeOfUnit": (b"{CFRange=qq}^{__CFCalendar=}Q",),
    "CFRunLoopRemoveSource": (b"v^{__CFRunLoop=}^{__CFRunLoopSource=}^{__CFString=}",),
    "CFSwapInt32": (b"II",),
    "CFRunLoopTimerGetNextFireDate": (b"d^{__CFRunLoopTimer=}",),
    "CFBitVectorGetCountOfBit": (b"q^{__CFBitVector=}{CFRange=qq}I",),
    "CFNotificationCenterGetDarwinNotifyCenter": (b"^{__CFNotificationCenter=}",),
    "CFPropertyListWrite": (
        b"q@^{__CFWriteStream=}qQ^^{__CFError=}",
        "",
        {
            "arguments": {
                4: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFDataAppendBytes": (
        b"v^{__CFData=}^vq",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CFDictionaryGetCount": (b"q^{__CFDictionary=}",),
    "CFLocaleCreateLocaleIdentifierFromWindowsLocaleCode": (
        b"^{__CFString=}^{__CFAllocator=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUserNotificationUpdate": (b"i^{__CFUserNotification=}dQ^{__CFDictionary=}",),
    "CFMessagePortInvalidate": (b"v^{__CFMessagePort=}",),
    "CFSwapInt64": (b"QQ",),
    "CFURLCreateWithFileSystemPath": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFString=}qZ",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringGetAttributes": (
        b"^{__CFDictionary=}^{__CFAttributedString=}q^{CFRange=qq}",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFSocketSetDefaultNameRegistryPortNumber": (b"vS",),
    "CFFileSecurityGetTypeID": (b"Q",),
    "CFBundleCopyResourceURLsOfType": (
        b"^{__CFArray=}^{__CFBundle=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFConvertFloat32SwappedToHost": (b"f{CFSwappedFloat32=I}",),
    "CFDictionaryReplaceValue": (b"v^{__CFDictionary=}@@",),
    "CFXMLTreeCreateFromDataWithError": (
        b"^{__CFTree=}^{__CFAllocator=}^{__CFData=}^{__CFURL=}Qq^^{__CFDictionary=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {5: {"type_modifier": "o"}},
        },
    ),
    "CFTimeZoneSetDefault": (b"v^{__CFTimeZone=}",),
    "CFArrayApplyFunction": (
        b"v^{__CFArray=}{CFRange=qq}^?@",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"@"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFMessagePortGetInvalidationCallBack": (
        b"^?^{__CFMessagePort=}",
        "",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"@"}, 1: {"type": b"^v"}},
                }
            }
        },
    ),
    "CFUserNotificationGetResponseDictionary": (
        b"^{__CFDictionary=}^{__CFUserNotification=}",
    ),
    "CFSwapInt32HostToLittle": (b"II",),
    "CFSocketInvalidate": (b"v^{__CFSocket=}",),
    "CFStringGetMostCompatibleMacStringEncoding": (b"II",),
    "CFRunLoopObserverIsValid": (b"Z^{__CFRunLoopObserver=}",),
    "CFStringInsert": (b"v^{__CFString=}q^{__CFString=}",),
    "CFXMLParserGetTypeID": (b"Q",),
    "CFMessagePortGetContext": (
        b"v^{__CFMessagePort=}^{CFMessagePortContext=q^v^?^?^?}",
    ),
    "CFStringIsEncodingAvailable": (b"ZI",),
    "CFStringGetLength": (b"q^{__CFString=}",),
    "CFURLCanBeDecomposed": (b"Z^{__CFURL=}",),
    "CFStringCreateWithCStringNoCopy": (
        b"^{__CFString=}^{__CFAllocator=}^tI^{__CFAllocator=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFReadStreamClose": (b"v^{__CFReadStream=}",),
    "CFBagCreate": (
        b"^{__CFBag=}^{__CFAllocator=}^^vq^{CFBagCallBacks=q^?^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopAddTimer": (b"v^{__CFRunLoop=}^{__CFRunLoopTimer=}^{__CFString=}",),
    "CFDictionaryGetValueIfPresent": (
        b"Z^{__CFDictionary=}@^@",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFArrayCreateCopy": (
        b"^{__CFArray=}^{__CFAllocator=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorGetLastIndexOfBit": (b"q^{__CFBitVector=}{CFRange=qq}I",),
    "CFDataCreateMutable": (
        b"^{__CFData=}^{__CFAllocator=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringAppendCString": (
        b"v^{__CFString=}^tI",
        "",
        {"arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}}},
    ),
    "CFLocaleGetIdentifier": (b"^{__CFString=}^{__CFLocale=}",),
    "CFStringConvertWindowsCodepageToEncoding": (b"II",),
    "CFTreeRemove": (b"v^{__CFTree=}",),
    "CFBundleCloseBundleResourceMap": (b"v^{__CFBundle=}i",),
    "CFStreamCreateBoundPair": (
        b"v^{__CFAllocator=}^^{__CFReadStream=}^^{__CFWriteStream=}q",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}},
        },
    ),
    "CFRunLoopTimerSetNextFireDate": (b"v^{__CFRunLoopTimer=}d",),
    "CFAllocatorSetDefault": (b"v^{__CFAllocator=}",),
    "CFPreferencesSetAppValue": (b"v^{__CFString=}@^{__CFString=}",),
    "CFTimeZoneGetNextDaylightSavingTimeTransition": (b"d^{__CFTimeZone=}d",),
    "CFDateFormatterGetFormat": (b"^{__CFString=}^{__CFDateFormatter=}",),
    "CFLocaleCreateLocaleIdentifierFromComponents": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPreferencesSynchronize": (b"Z^{__CFString=}^{__CFString=}^{__CFString=}",),
    "CFReadStreamCopyDispatchQueue": (
        b"@^{__CFReadStream=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetMaximumSizeOfFileSystemRepresentation": (b"q^{__CFString=}",),
    "CFBundleCreate": (
        b"^{__CFBundle=}^{__CFAllocator=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLGetPortNumber": (b"i^{__CFURL=}",),
    "CFStringAppendCharacters": (
        b"v^{__CFString=}^Tq",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}}},
    ),
    "CFArrayGetLastIndexOfValue": (b"q^{__CFArray=}{CFRange=qq}@",),
    "CFRunLoopTimerCreate": (
        b"^{__CFRunLoopTimer=}^{__CFAllocator=}ddQq^?^{CFRunLoopTimerContext=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFRunLoopTimer=}"},
                            1: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFFileDescriptorCreateRunLoopSource": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}^{__CFFileDescriptor=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringHasSuffix": (b"Z^{__CFString=}^{__CFString=}",),
    "CFEqual": (b"Z@@",),
    "CFRunLoopGetNextTimerFireDate": (b"d^{__CFRunLoop=}^{__CFString=}",),
    "CFErrorCreate": (
        b"^{__CFError=}^{__CFAllocator=}^{__CFString=}q^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateByCombiningStrings": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFArray=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateWithFileSystemRepresentation": (
        b"^{__CFString=}^{__CFAllocator=}^t",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFBundleGetPlugIn": (b"^{__CFBundle=}^{__CFBundle=}",),
    "CFAllocatorGetPreferredSizeForSize": (b"q^{__CFAllocator=}qQ",),
    "CFDateFormatterSetFormat": (b"v^{__CFDateFormatter=}^{__CFString=}",),
    "CFBinaryHeapApplyFunction": (
        b"v^{__CFBinaryHeap=}^?@",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"@"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFRunLoopTimerSetTolerance": (b"v^{__CFRunLoopTimer=}d",),
    "CFStringGetBytes": (
        b"q^{__CFString=}{CFRange=qq}ICZ^tq^q",
        "",
        {
            "arguments": {
                5: {"c_array_length_in_arg": (6, 7), "type_modifier": "o"},
                7: {"type_modifier": "o"},
            }
        },
    ),
    "CFLocaleCopyAvailableLocaleIdentifiers": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateArrayBySeparatingStrings": (
        b"^{__CFArray=}^{__CFAllocator=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetDoubleValue": (b"d^{__CFString=}",),
    "CFBundleIsExecutableLoadable": (b"Z^{__CFBundle=}",),
    "CFSetGetCount": (b"q^{__CFSet=}",),
    "CFURLCreateWithBytes": (
        b"^{__CFURL=}^{__CFAllocator=}^vqI^{__CFURL=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFStringCompareWithOptionsAndLocale": (
        b"q^{__CFString=}^{__CFString=}{CFRange=qq}Q^{__CFLocale=}",
    ),
    "CFPropertyListCreateFromStream": (
        b"@^{__CFAllocator=}^{__CFReadStream=}qQ^q^^{__CFString=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {4: {"type_modifier": "o"}, 5: {"type_modifier": "o"}},
        },
    ),
    "CFAbsoluteTimeAddGregorianUnits": (
        b"dd^{__CFTimeZone=}{CFGregorianUnits=iiiiid}",
    ),
    "CFLocaleCopyCurrent": (
        b"^{__CFLocale=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDateFormatterGetLocale": (b"^{__CFLocale=}^{__CFDateFormatter=}",),
    "CFURLEnumeratorGetSourceDidChange": (b"Z^{__CFURLEnumerator=}",),
    "CFNullGetTypeID": (b"Q",),
    "CFStringUppercase": (b"v^{__CFString=}^{__CFLocale=}",),
    "CFTreeGetFirstChild": (b"^{__CFTree=}^{__CFTree=}",),
    "CFAbsoluteTimeGetDayOfYear": (b"id^{__CFTimeZone=}",),
    "CFURLCreateFromFileSystemRepresentation": (
        b"^{__CFURL=}^{__CFAllocator=}^tqZ",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFBundleGetInfoDictionary": (b"^{__CFDictionary=}^{__CFBundle=}",),
    "CFByteOrderGetCurrent": (b"q",),
    "CFAttributedStringEndEditing": (b"v^{__CFAttributedString=}",),
    "CFUserNotificationCancel": (b"i^{__CFUserNotification=}",),
    "CFUserNotificationSecureTextField": (b"Qq",),
    "CFBitVectorCreate": (
        b"^{__CFBitVector=}^{__CFAllocator=}^Cq",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"c_array_of_variable_length": True, "type_modifier": "n"}
            },
        },
    ),
    "CFCharacterSetRemoveCharactersInRange": (b"v^{__CFCharacterSet=}{CFRange=qq}",),
    "CFMachPortSetInvalidationCallBack": (
        b"v^{__CFMachPort=}^?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFMachPort=}"},
                            1: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CFCharacterSetAddCharactersInString": (b"v^{__CFCharacterSet=}^{__CFString=}",),
    "CFBitVectorGetBitAtIndex": (b"I^{__CFBitVector=}q",),
    "CFURLIsFileReferenceURL": (b"Z^{__CFURL=}",),
    "CFURLCopyPath": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFArrayGetFirstIndexOfValue": (b"q^{__CFArray=}{CFRange=qq}@",),
    "CFCharacterSetCreateWithCharactersInRange": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}{CFRange=qq}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetIsLongCharacterMember": (b"Z^{__CFCharacterSet=}I",),
    "CFUUIDCreateFromUUIDBytes": (
        b"^{__CFUUID=}^{__CFAllocator=}{CFUUIDBytes=CCCCCCCCCCCCCCCC}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopAddSource": (b"v^{__CFRunLoop=}^{__CFRunLoopSource=}^{__CFString=}",),
    "CFDictionaryContainsValue": (b"Z^{__CFDictionary=}@",),
    "CFTimeZoneCopyKnownNames": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleCopyPreferredLocalizationsFromArray": (
        b"^{__CFArray=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorFlipBitAtIndex": (b"v^{__CFBitVector=}q",),
    "CFPropertyListCreateXMLData": (
        b"^{__CFData=}^{__CFAllocator=}@",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateResourcePropertyForKeyFromBookmarkData": (
        b"@^{__CFAllocator=}^{__CFString=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDateGetAbsoluteTime": (b"d^{__CFDate=}",),
    "CFNumberIsFloatType": (b"Z^{__CFNumber=}",),
    "CFTreePrependChild": (b"v^{__CFTree=}^{__CFTree=}",),
    "CFRunLoopWakeUp": (b"v^{__CFRunLoop=}",),
    "CFDateFormatterCreateStringWithDate": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFDateFormatter=}^{__CFDate=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetHasMemberInPlane": (b"Z^{__CFCharacterSet=}q",),
    "CFURLCopyResourceSpecifier": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringFold": (b"v^{__CFString=}Q^{__CFLocale=}",),
    "CFStringTokenizerCopyCurrentTokenAttribute": (
        b"@^{__CFStringTokenizer=}Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNotificationCenterRemoveEveryObserver": (b"v^{__CFNotificationCenter=}@",),
    "CFMessagePortGetName": (b"^{__CFString=}^{__CFMessagePort=}",),
    "CFURLCopyPassword": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFWriteStreamClose": (b"v^{__CFWriteStream=}",),
    "CFMessagePortCreateRunLoopSource": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}^{__CFMessagePort=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetCString": (
        b"Z^{__CFString=}^tqI",
        "",
        {"arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "o"}}},
    ),
    "_CFAutoreleasePoolPrintPools": (b"v",),
    "CFPropertyListCreateData": (
        b"^{__CFData=}^{__CFAllocator=}@qQ^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "CFURLCopyQueryString": (
        b"^{__CFString=}^{__CFURL=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopTimerCreateWithHandler": (
        b"^{__CFRunLoopTimer=}^{__CFAllocator=}ddQq@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{__CFRunLoopTimer=}"},
                        },
                    }
                }
            },
        },
    ),
    "CFTimeZoneCopyAbbreviationDictionary": (
        b"^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringConvertEncodingToIANACharSetName": (b"^{__CFString=}I",),
    "CFSwapInt16LittleToHost": (b"SS",),
    "CFBundleCopyLocalizedStringForLocalizations": (
        b"^{__CFString=}^{__CFBundle=}^{__CFString=}^{__CFString=}^{__CFString=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFSocketCopyPeerAddress": (
        b"^{__CFData=}^{__CFSocket=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateFromFileSystemRepresentationRelativeToBase": (
        b"^{__CFURL=}^{__CFAllocator=}^tqZ^{__CFURL=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFAttributedStringSetAttributes": (
        b"v^{__CFAttributedString=}{CFRange=qq}^{__CFDictionary=}Z",
    ),
    "CFNumberFormatterCopyProperty": (
        b"@^{__CFNumberFormatter=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFSocketGetTypeID": (b"Q",),
    "CFURLGetBaseURL": (b"^{__CFURL=}^{__CFURL=}",),
    "CFSetGetCountOfValue": (b"q^{__CFSet=}@",),
    "CFWriteStreamSetProperty": (b"Z^{__CFWriteStream=}^{__CFString=}@",),
    "CFDictionarySetValue": (b"v^{__CFDictionary=}@@",),
    "CFRunLoopSourceGetTypeID": (b"Q",),
    "CFWriteStreamCopyError": (
        b"^{__CFError=}^{__CFWriteStream=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorSetBits": (b"v^{__CFBitVector=}{CFRange=qq}I",),
    "CFURLCreateBookmarkDataFromAliasRecord": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringRemoveAttribute": (
        b"v^{__CFAttributedString=}{CFRange=qq}^{__CFString=}",
    ),
    "CFURLCreateFromFSRef": (
        b"^{__CFURL=}^{__CFAllocator=}^{FSRef=[80C]}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "n"}},
        },
    ),
    "CFBitVectorFlipBits": (b"v^{__CFBitVector=}{CFRange=qq}",),
    "CFCalendarCopyCurrent": (
        b"^{__CFCalendar=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopAddObserver": (b"v^{__CFRunLoop=}^{__CFRunLoopObserver=}^{__CFString=}",),
    "CFURLCreateCopyDeletingPathExtension": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFMessagePortIsValid": (b"Z^{__CFMessagePort=}",),
    "CFBundleCopySupportFilesDirectoryURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNumberGetType": (b"q^{__CFNumber=}",),
    "CFDataCreateCopy": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDictionaryCreateMutableCopy": (
        b"^{__CFDictionary=}^{__CFAllocator=}q^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringConvertEncodingToNSStringEncoding": (b"QI",),
    "CFXMLParserGetSourceURL": (b"^{__CFURL=}^{__CFXMLParser=}",),
    "CFSetContainsValue": (b"Z^{__CFSet=}@",),
    "CFBundleCopyInfoDictionaryForURL": (
        b"^{__CFDictionary=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFMessagePortSetInvalidationCallBack": (
        b"v^{__CFMessagePort=}^?",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFMessagePort=}"},
                            1: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CFTreeRemoveAllChildren": (b"v^{__CFTree=}",),
    "CFFileDescriptorIsValid": (b"Z^{__CFFileDescriptor=}",),
    "CFSetGetValueIfPresent": (
        b"Z^{__CFSet=}@^@",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFBinaryHeapGetCount": (b"q^{__CFBinaryHeap=}",),
    "CFRunLoopContainsObserver": (
        b"Z^{__CFRunLoop=}^{__CFRunLoopObserver=}^{__CFString=}",
    ),
    "CFRunLoopObserverGetOrder": (b"q^{__CFRunLoopObserver=}",),
    "CFBagReplaceValue": (b"v^{__CFBag=}@",),
    "CFTreeSetContext": (b"v^{__CFTree=}^{CFTreeContext=q^v^?^?^?}",),
    "CFReadStreamCopyProperty": (
        b"@^{__CFReadStream=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringIsHyphenationAvailableForLocale": (b"Z^{__CFLocale=}",),
    "CFWriteStreamCopyDispatchQueue": (
        b"@^{__CFWriteStream=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopSourceCreate": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}q^{CFRunLoopSourceContext=q^v^?^?^?^?^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetInvert": (b"v^{__CFCharacterSet=}",),
    "CFBundleIsExecutableLoadableForURL": (b"Z^{__CFURL=}",),
    "CFMachPortInvalidate": (b"v^{__CFMachPort=}",),
    "CFSwapInt32BigToHost": (b"II",),
    "CFDataReplaceBytes": (
        b"v^{__CFData=}{CFRange=qq}^vq",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 3, "type_modifier": "n"}}},
    ),
    "CFDataGetBytePtr": (
        b"^v^{__CFData=}",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CFSocketDisableCallBacks": (b"v^{__CFSocket=}Q",),
    "CFBundleCopyLocalizationsForURL": (
        b"^{__CFArray=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFReadStreamGetBuffer": (
        b"^v^{__CFReadStream=}q^q",
        "",
        {
            "retval": {"c_array_length_in_arg": 2},
            "arguments": {2: {"type_modifier": "o"}},
        },
    ),
    "CFErrorGetDomain": (b"^{__CFString=}^{__CFError=}",),
    "CFStringHasPrefix": (b"Z^{__CFString=}^{__CFString=}",),
    "CFTimeZoneIsDaylightSavingTime": (b"Z^{__CFTimeZone=}d",),
    "CFWriteStreamCanAcceptBytes": (b"Z^{__CFWriteStream=}",),
    "CFWriteStreamOpen": (b"Z^{__CFWriteStream=}",),
    "CFBitVectorSetCount": (b"v^{__CFBitVector=}q",),
    "CFErrorCreateWithUserInfoKeysAndValues": (
        b"^{__CFError=}^{__CFAllocator=}^{__CFString=}q^@^@q",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {"c_array_length_in_arg": 5, "type_modifier": "n"},
                4: {"c_array_length_in_arg": 5, "type_modifier": "n"},
            },
        },
    ),
    "CFUserNotificationCreate": (
        b"^{__CFUserNotification=}^{__CFAllocator=}dQ^i^{__CFDictionary=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "CFURLResourceIsReachable": (
        b"Z^{__CFURL=}^^{__CFError=}",
        "",
        {
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFStringIsSurrogateHighCharacter": (b"ZT",),
    "CFPropertyListCreateWithData": (
        b"@^{__CFAllocator=}^{__CFData=}Q^q^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {"type_modifier": "o"},
                4: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            },
        },
    ),
    "CFSocketGetDefaultNameRegistryPortNumber": (b"S",),
    "CFBundleCopyLocalizationsForPreferences": (
        b"^{__CFArray=}^{__CFArray=}^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopObserverInvalidate": (b"v^{__CFRunLoopObserver=}",),
    "CFURLGetFSRef": (
        b"Z^{__CFURL=}^{FSRef=[80C]}",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CFURLCreateCopyDeletingLastPathComponent": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleCreateBundlesFromDirectory": (
        b"^{__CFArray=}^{__CFAllocator=}^{__CFURL=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringFindCharacterFromSet": (
        b"Z^{__CFString=}^{__CFCharacterSet=}{CFRange=qq}Q^{CFRange=qq}",
        "",
        {"arguments": {4: {"type_modifier": "o"}}},
    ),
    "CFAttributedStringCreateMutable": (
        b"^{__CFAttributedString=}^{__CFAllocator=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFTreeGetContext": (b"v^{__CFTree=}^{CFTreeContext=q^v^?^?^?}",),
    "CFArrayCreateMutable": (
        b"^{__CFArray=}^{__CFAllocator=}q^{CFArrayCallBacks=q^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFShow": (b"v@",),
    "CFFileSecuritySetOwner": (b"Z^{__CFFileSecurity=}I",),
    "CFSocketCopyAddress": (
        b"^{__CFData=}^{__CFSocket=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBagGetValues": (
        b"v^{__CFBag=}^@",
        "",
        {"arguments": {1: {"c_array_of_variable_length": True, "type_modifier": "o"}}},
    ),
    "CFFileSecuritySetOwnerUUID": (b"Z^{__CFFileSecurity=}^{__CFUUID=}",),
    "CFLocaleCreateCanonicalLocaleIdentifierFromScriptManagerCodes": (
        b"^{__CFString=}^{__CFAllocator=}ss",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDictionaryRemoveValue": (b"v^{__CFDictionary=}@",),
    "CFWriteStreamSetClient": (
        b"Z^{__CFWriteStream=}Q^?^{CFStreamClientContext=q^v^?^?^?}",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFWriteStream=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            }
        },
    ),
    "CFRunLoopSourceIsValid": (b"Z^{__CFRunLoopSource=}",),
    "CFCharacterSetIsCharacterMember": (b"Z^{__CFCharacterSet=}T",),
    "CFTreeGetChildCount": (b"q^{__CFTree=}",),
    "CFURLSetTemporaryResourcePropertyForKey": (b"v^{__CFURL=}^{__CFString=}@",),
    "CFConvertDoubleHostToSwapped": (b"{CFSwappedFloat64=Q}d",),
    "CFSetGetValue": (b"@^{__CFSet=}@",),
    "CFMessagePortSendRequest": (
        b"i^{__CFMessagePort=}i^{__CFData=}dd^{__CFString=}^^{__CFData=}",
        "",
        {"arguments": {6: {"type_modifier": "o"}}},
    ),
    "CFUUIDGetConstantUUIDWithBytes": (
        b"^{__CFUUID=}^{__CFAllocator=}CCCCCCCCCCCCCCCC",
    ),
    "CFSocketCreateRunLoopSource": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}^{__CFSocket=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFWriteStreamCreateWithBuffer": (
        b"^{__CFWriteStream=}^{__CFAllocator=}^vq",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFXMLTreeCreateXMLData": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFTree=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCopyUserName": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCopyAbsoluteURL": (
        b"^{__CFURL=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNumberGetTypeID": (b"Q",),
    "CFStringCompareWithOptions": (b"q^{__CFString=}^{__CFString=}{CFRange=qq}Q",),
    "CFWriteStreamGetTypeID": (b"Q",),
    "CFSwapInt16": (b"SS",),
    "CFDateGetTimeIntervalSinceDate": (b"d^{__CFDate=}^{__CFDate=}",),
    "CFAttributedStringGetString": (b"^{__CFString=}^{__CFAttributedString=}",),
    "CFURLCopyNetLocation": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringFind": (b"{CFRange=qq}^{__CFString=}^{__CFString=}Q",),
    "CFConvertFloat64HostToSwapped": (b"{CFSwappedFloat64=Q}d",),
    "CFSetReplaceValue": (b"v^{__CFSet=}@",),
    "CFAttributedStringGetBidiLevelsAndResolvedDirections": (
        b"B^{__CFAttributedString=}{CFRange=qq}c^C^C",
        "",
        {
            "arguments": {
                3: {"c_array_length_in_arg": 1, "type_modifier": "o"},
                4: {"c_array_length_in_arg": 1, "type_modifier": "o"},
            }
        },
    ),
    "CFTimeZoneCreate": (
        b"^{__CFTimeZone=}^{__CFAllocator=}^{__CFString=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCopyScheme": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLParserParse": (b"Z^{__CFXMLParser=}",),
    "CFRunLoopRemoveTimer": (b"v^{__CFRunLoop=}^{__CFRunLoopTimer=}^{__CFString=}",),
    "CFPreferencesAppValueIsForced": (b"Z^{__CFString=}^{__CFString=}",),
    "CFSocketCreate": (
        b"^{__CFSocket=}^{__CFAllocator=}iiiQ^?^{CFSocketContext=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFSocket=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^{__CFData=}"},
                            3: {"type": b"^v"},
                            4: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFNotificationCenterGetTypeID": (b"Q",),
    "CFURLCreateStringByReplacingPercentEscapes": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorContainsBit": (b"Z^{__CFBitVector=}{CFRange=qq}I",),
    "CFMachPortCreateWithPort": (
        b"^{__CFMachPort=}^{__CFAllocator=}I^?^{CFMachPortContext=q^v^?^?^?}^Z",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFMachPort=}"},
                            1: {"type": b"^v"},
                            2: {"type": b"q"},
                            3: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFPreferencesAppSynchronize": (b"Z^{__CFString=}",),
    "CFFileDescriptorGetTypeID": (b"Q",),
    "CFBundleCopyBundleLocalizations": (
        b"^{__CFArray=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFFileSecurityCreate": (
        b"^{__CFFileSecurity=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFHash": (b"Q@",),
    "CFCharacterSetIntersect": (b"v^{__CFCharacterSet=}^{__CFCharacterSet=}",),
    "CFXMLNodeCreateCopy": (
        b"^{__CFXMLNode=}^{__CFAllocator=}^{__CFXMLNode=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFArrayCreate": (
        b"^{__CFArray=}^{__CFAllocator=}^^vq^{CFArrayCallBacks=q^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBooleanGetValue": (b"Z^{__CFBoolean=}",),
    "CFArrayContainsValue": (b"Z^{__CFArray=}{CFRange=qq}@",),
    "CFSwapInt32HostToBig": (b"II",),
    "CFURLWriteDataAndPropertiesToResource": (
        b"Z^{__CFURL=}^{__CFData=}^{__CFDictionary=}^i",
        "",
        {"arguments": {3: {"type_modifier": "o"}}},
    ),
    "CFArrayInsertValueAtIndex": (b"v^{__CFArray=}q@",),
    "CFDictionaryCreateMutable": (
        b"^{__CFDictionary=}^{__CFAllocator=}q^{CFDictionaryKeyCallBacks=q^?^?^?^?^?}^{CFDictionaryValueCallBacks=q^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAllocatorGetTypeID": (b"Q",),
    "CFReadStreamRead": (
        b"q^{__CFReadStream=}^vq",
        "",
        {
            "arguments": {
                1: {
                    "c_array_length_in_result": True,
                    "type_modifier": "o",
                    "c_array_length_in_arg": 2,
                }
            }
        },
    ),
    "CFDataGetBytes": (
        b"v^{__CFData=}{CFRange=qq}^v",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "o"}}},
    ),
    "CFStringCreateWithCharactersNoCopy": (
        b"^{__CFString=}^{__CFAllocator=}^Tq^{__CFAllocator=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFStringGetLongCharacterForSurrogatePair": (b"ITT",),
    "CFSetAddValue": (b"v^{__CFSet=}@",),
    "CFFileSecuritySetMode": (b"Z^{__CFFileSecurity=}S",),
    "CFURLCreateStringByAddingPercentEscapes": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFString=}^{__CFString=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetIntValue": (b"i^{__CFString=}",),
    "CFDictionaryGetCountOfValue": (b"q^{__CFDictionary=}@",),
    "CFDataGetMutableBytePtr": (
        b"^v^{__CFData=}",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CFURLCreateAbsoluteURLWithBytes": (
        b"^{__CFURL=}^{__CFAllocator=}^vqI^{__CFURL=}Z",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFBundleOpenBundleResourceFiles": (
        b"i^{__CFBundle=}^i^i",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CFStringCreateStringWithValidatedFormat": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFDictionary=}^{__CFString=}^{__CFString=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"printf_format": True},
                3: {"printf_format": True},
                4: {
                    "null_accepted": True,
                    "already_cfretained": True,
                    "type_modifier": "o",
                },
            },
            "variadic": True,
        },
    ),
    "CFRunLoopTimerGetInterval": (b"d^{__CFRunLoopTimer=}",),
    "CFFileSecurityGetOwner": (
        b"Z^{__CFFileSecurity=}^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CFCalendarGetIdentifier": (b"^{__CFString=}^{__CFCalendar=}",),
    "CFStringCompare": (b"q^{__CFString=}^{__CFString=}Q",),
    "CFURLClearResourcePropertyCache": (b"v^{__CFURL=}",),
    "CFRunLoopSourceInvalidate": (b"v^{__CFRunLoopSource=}",),
    "CFCalendarCreateWithIdentifier": (
        b"^{__CFCalendar=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFMachPortGetPort": (b"I^{__CFMachPort=}",),
    "CFBinaryHeapRemoveMinimumValue": (b"v^{__CFBinaryHeap=}",),
    "CFBagRemoveValue": (b"v^{__CFBag=}@",),
    "CFStringReplaceAll": (b"v^{__CFString=}^{__CFString=}",),
    "CFArraySetValueAtIndex": (b"v^{__CFArray=}q@",),
    "CFBundleCopyExecutableArchitecturesForURL": (
        b"^{__CFArray=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopObserverDoesRepeat": (b"Z^{__CFRunLoopObserver=}",),
    "CFDateCompare": (b"q^{__CFDate=}^{__CFDate=}^v",),
    "CFGregorianDateIsValid": (b"Z{CFGregorianDate=iccccd}Q",),
    "CFAutorelease": (b"@@",),
    "CFRunLoopTimerInvalidate": (b"v^{__CFRunLoopTimer=}",),
    "CFDictionaryGetCountOfKey": (b"q^{__CFDictionary=}@",),
    "CFStringGetCharacterAtIndex": (b"T^{__CFString=}q",),
    "CFStringCreateWithCString": (
        b"^{__CFString=}^{__CFAllocator=}^tI",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFReadStreamCopyError": (
        b"^{__CFError=}^{__CFReadStream=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUserNotificationCheckBoxChecked": (b"Qq",),
    "CFAttributedStringCreate": (
        b"^{__CFAttributedString=}^{__CFAllocator=}^{__CFString=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringFindWithOptions": (
        b"Z^{__CFString=}^{__CFString=}{CFRange=qq}Q^{CFRange=qq}",
        "",
        {"arguments": {4: {"type_modifier": "o"}}},
    ),
    "CFSetRemoveAllValues": (b"v^{__CFSet=}",),
    "CFArraySortValues": (
        b"v^{__CFArray=}{CFRange=qq}^?@",
        "",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFCalendarGetFirstWeekday": (b"q^{__CFCalendar=}",),
    "CFStreamCreatePairWithPeerSocketSignature": (
        b"v^{__CFAllocator=}^{CFSocketSignature=iii^{__CFData=}}^^{__CFReadStream=}^^{__CFWriteStream=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                1: {"type_modifier": "n"},
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
            },
        },
    ),
    "CFURLSetResourcePropertiesForKeys": (
        b"Z^{__CFURL=}^{__CFDictionary=}^^{__CFError=}",
        "",
        {
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFXMLParserGetLocation": (b"q^{__CFXMLParser=}",),
    "CFStringTokenizerGetCurrentTokenRange": (b"{CFRange=qq}^{__CFStringTokenizer=}",),
    "CFBagContainsValue": (b"Z^{__CFBag=}@",),
    "CFUUIDCreateWithBytes": (
        b"^{__CFUUID=}^{__CFAllocator=}CCCCCCCCCCCCCCCC",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFArrayGetCount": (b"q^{__CFArray=}",),
    "CFArrayCreateMutableCopy": (
        b"^{__CFArray=}^{__CFAllocator=}q^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFSetSetValue": (b"v^{__CFSet=}@",),
    "CFSwapInt64HostToBig": (b"QQ",),
    "CFCharacterSetUnion": (b"v^{__CFCharacterSet=}^{__CFCharacterSet=}",),
    "CFFileSecurityCopyGroupUUID": (
        b"Z^{__CFFileSecurity=}^^{__CFUUID=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CFAttributedStringSetAttribute": (
        b"v^{__CFAttributedString=}{CFRange=qq}^{__CFString=}@",
    ),
    "CFReadStreamOpen": (b"Z^{__CFReadStream=}",),
    "CFXMLNodeGetVersion": (b"q^{__CFXMLNode=}",),
    "CFStringCreateWithBytesNoCopy": (
        b"^{__CFString=}^{__CFAllocator=}^tqIZ^{__CFAllocator=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFBundleGetTypeID": (b"Q",),
    "CFURLDestroyResource": (
        b"Z^{__CFURL=}^i",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CFBagSetValue": (b"v^{__CFBag=}@",),
    "CFURLWriteBookmarkDataToFile": (
        b"Z^{__CFData=}^{__CFURL=}Q^^{__CFError=}",
        "",
        {
            "arguments": {
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFLocaleCreate": (
        b"^{__CFLocale=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFGetTypeID": (b"Q@",),
    "CFURLCopyFragment": (
        b"^{__CFString=}^{__CFURL=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFWriteStreamCreateWithFile": (
        b"^{__CFWriteStream=}^{__CFAllocator=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAbsoluteTimeGetDifferenceAsGregorianUnits": (
        b"{CFGregorianUnits=iiiiid}dd^{__CFTimeZone=}Q",
    ),
    "CFReadStreamCreateWithFile": (
        b"^{__CFReadStream=}^{__CFAllocator=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleCopyLocalizedString": (
        b"^{__CFString=}^{__CFBundle=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFErrorCopyRecoverySuggestion": (
        b"^{__CFString=}^{__CFError=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateBookmarkData": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFURL=}Q^{__CFArray=}^{__CFURL=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                5: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "CFStringCreateWithBytes": (
        b"^{__CFString=}^{__CFAllocator=}^tqIZ",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFRunLoopSourceGetOrder": (b"q^{__CFRunLoopSource=}",),
    "CFBundleLoadExecutable": (b"Z^{__CFBundle=}",),
    "CFStringCreateCopy": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateFilePathURL": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFURL=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "CFUserNotificationDisplayNotice": (
        b"idQ^{__CFURL=}^{__CFURL=}^{__CFURL=}^{__CFString=}^{__CFString=}^{__CFString=}",
    ),
    "CFUserNotificationGetResponseValue": (
        b"^{__CFString=}^{__CFUserNotification=}^{__CFString=}q",
    ),
    "CFRunLoopContainsTimer": (b"Z^{__CFRunLoop=}^{__CFRunLoopTimer=}^{__CFString=}",),
    "CFPreferencesSetValue": (
        b"v^{__CFString=}@^{__CFString=}^{__CFString=}^{__CFString=}",
    ),
    "CFReadStreamGetStatus": (b"q^{__CFReadStream=}",),
    "CFCopyTypeIDDescription": (
        b"^{__CFString=}Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFConvertFloatHostToSwapped": (b"{CFSwappedFloat32=I}f",),
    "CFCalendarDecomposeAbsoluteTime": (
        b"Z^{__CFCalendar=}d^c",
        "",
        {"variadic": True},
    ),
    "CFBinaryHeapCreate": (
        b"^{__CFBinaryHeap=}^{__CFAllocator=}q^{CFBinaryHeapCallBacks=q^?^?^?^?}^{CFBinaryHeapCompareContext=q^v^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateExternalRepresentation": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFString=}IC",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDateFormatterCreateISO8601Formatter": (
        b"^{__CFDateFormatter=}^{__CFAllocator=}Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateWithFormat": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFDictionary=}^{__CFString=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"printf_format": True}},
            "variadic": True,
        },
    ),
    "CFBundleCopyResourceURLsOfTypeForLocalization": (
        b"^{__CFArray=}^{__CFBundle=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFConvertFloat32HostToSwapped": (b"{CFSwappedFloat32=I}f",),
    "CFRunLoopObserverCreateWithHandler": (
        b"^{__CFRunLoopObserver=}^{__CFAllocator=}QZq@?",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^{__CFRunLoopObserver=}"},
                            2: {"type": b"Q"},
                        },
                    }
                }
            },
        },
    ),
    "CFDataCreate": (
        b"^{__CFData=}^{__CFAllocator=}^vq",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFSwapInt16HostToLittle": (b"SS",),
    "CFSetCreate": (
        b"^{__CFSet=}^{__CFAllocator=}^^vq^{CFSetCallBacks=q^?^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFGregorianDateGetAbsoluteTime": (b"d{CFGregorianDate=iccccd}^{__CFTimeZone=}",),
    "CFStringGetListOfAvailableEncodings": (
        b"^I",
        "",
        {"retval": {"c_array_of_variable_length": True}},
    ),
    "CFRunLoopTimerGetContext": (
        b"v^{__CFRunLoopTimer=}^{CFRunLoopTimerContext=q^v^?^?^?}",
    ),
    "CFXMLParserAbort": (b"v^{__CFXMLParser=}q^{__CFString=}",),
    "CFPropertyListCreateFromXMLData": (
        b"@^{__CFAllocator=}^{__CFData=}Q^^{__CFString=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "CFStringFindAndReplace": (
        b"q^{__CFString=}^{__CFString=}^{__CFString=}{CFRange=qq}Q",
    ),
    "CFDictionaryGetTypeID": (b"Q",),
    "CFBundleGetDevelopmentRegion": (b"^{__CFString=}^{__CFBundle=}",),
    "CFBundleGetMainBundle": (b"^{__CFBundle=}",),
    "CFXMLNodeCreate": (
        b"^{__CFXMLNode=}^{__CFAllocator=}q^{__CFString=}^vq",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleUnloadExecutable": (b"v^{__CFBundle=}",),
    "CFArrayGetCountOfValue": (b"q^{__CFArray=}{CFRange=qq}@",),
    "CFRunLoopTimerIsValid": (b"Z^{__CFRunLoopTimer=}",),
    "CFConvertFloat64SwappedToHost": (b"d{CFSwappedFloat64=Q}",),
    "CFReadStreamHasBytesAvailable": (b"Z^{__CFReadStream=}",),
    "CFDataSetLength": (b"v^{__CFData=}q",),
    "CFStringTokenizerCreate": (
        b"^{__CFStringTokenizer=}^{__CFAllocator=}^{__CFString=}{CFRange=qq}Q^{__CFLocale=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFMachPortGetTypeID": (b"Q",),
    "CFTreeGetChildAtIndex": (b"^{__CFTree=}^{__CFTree=}q",),
    "CFSwapInt16BigToHost": (b"SS",),
    "CFStringCreateWithCharacters": (
        b"^{__CFString=}^{__CFAllocator=}^Tq",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_delimited_by_null": True, "type_modifier": "n"}},
        },
    ),
    "CFUserNotificationCreateRunLoopSource": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}^{__CFUserNotification=}^?q",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFUserNotification=}"},
                            1: {"type": b"q"},
                        },
                    },
                    "callable_retained": True,
                }
            },
        },
    ),
    "CFStringTrimWhitespace": (b"v^{__CFString=}",),
    "CFMessagePortCreateRemote": (
        b"^{__CFMessagePort=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringDelete": (b"v^{__CFString=}{CFRange=qq}",),
    "CFBundleCopyResourceURLInDirectory": (
        b"^{__CFURL=}^{__CFURL=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFTreeFindRoot": (b"^{__CFTree=}^{__CFTree=}",),
    "CFLocaleCopyDisplayNameForPropertyValue": (
        b"^{__CFString=}^{__CFLocale=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringTokenizerGetTypeID": (b"Q",),
    "CFSocketGetSocketFlags": (b"Q^{__CFSocket=}",),
    "CFLocaleGetLanguageLineDirection": (b"q^{__CFString=}",),
    "CFCopyHomeDirectoryURL": (
        b"^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUUIDCreateFromString": (
        b"^{__CFUUID=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPreferencesCopyApplicationList": (
        b"^{__CFArray=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFErrorCopyUserInfo": (
        b"^{__CFDictionary=}^{__CFError=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFMachPortIsValid": (b"Z^{__CFMachPort=}",),
    "CFCalendarComposeAbsoluteTime": (b"Z^{__CFCalendar=}^d^c", "", {"variadic": True}),
    "CFReadStreamUnscheduleFromRunLoop": (
        b"v^{__CFReadStream=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "CFDictionaryGetValue": (b"@^{__CFDictionary=}@",),
    "CFReadStreamCreateWithBytesNoCopy": (
        b"^{__CFReadStream=}^{__CFAllocator=}^vq^{__CFAllocator=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFSwapInt32LittleToHost": (b"II",),
    "CFBinaryHeapRemoveAllValues": (b"v^{__CFBinaryHeap=}",),
    "CFWriteStreamGetStatus": (b"q^{__CFWriteStream=}",),
    "CFURLCreateFileReferenceURL": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFURL=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "CFRunLoopObserverGetActivities": (b"Q^{__CFRunLoopObserver=}",),
    "CFTimeZoneCreateWithTimeIntervalFromGMT": (
        b"^{__CFTimeZone=}^{__CFAllocator=}d",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPropertyListCreateWithStream": (
        b"@^{__CFAllocator=}^{__CFReadStream=}qQ^q^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {"type_modifier": "o"},
                5: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            },
        },
    ),
    "CFFileSecurityClearProperties": (b"Z^{__CFFileSecurity=}Q",),
    "CFURLCopyResourcePropertyForKey": (
        b"Z^{__CFURL=}^{__CFString=}^@^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {"already_cfretained": True, "type_modifier": "o"},
                3: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                },
            },
        },
    ),
    "CFDateFormatterCopyProperty": (
        b"@^{__CFDateFormatter=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFSwapInt64LittleToHost": (b"QQ",),
    "CFUserNotificationReceiveResponse": (
        b"i^{__CFUserNotification=}d^Q",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFNumberFormatterGetLocale": (b"^{__CFLocale=}^{__CFNumberFormatter=}",),
    "CFURLStartAccessingSecurityScopedResource": (b"Z^{__CFURL=}",),
    "CFMachPortCreateRunLoopSource": (
        b"^{__CFRunLoopSource=}^{__CFAllocator=}^{__CFMachPort=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLGetBytes": (
        b"q^{__CFURL=}^Cq",
        "",
        {
            "arguments": {
                1: {
                    "c_array_length_in_arg": 2,
                    "c_array_length_in_result": True,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "CFFileDescriptorGetNativeDescriptor": (b"i^{__CFFileDescriptor=}",),
    "CFTimeZoneSetAbbreviationDictionary": (b"v^{__CFDictionary=}",),
    "CFUserNotificationGetTypeID": (b"Q",),
    "CFTimeZoneCopySystem": (
        b"^{__CFTimeZone=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFShowStr": (b"v^{__CFString=}",),
    "CFURLEnumeratorGetTypeID": (b"Q",),
    "CFBundleCopyResourceURL": (
        b"^{__CFURL=}^{__CFBundle=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorGetBits": (
        b"v^{__CFBitVector=}{CFRange=qq}^C",
        "",
        {"arguments": {2: {"c_array_of_variable_length": True, "type_modifier": "o"}}},
    ),
    "CFFileSecuritySetGroup": (b"Z^{__CFFileSecurity=}I",),
    "CFNotificationCenterGetLocalCenter": (b"^{__CFNotificationCenter=}",),
    "CFTimeZoneGetData": (b"^{__CFData=}^{__CFTimeZone=}",),
    "CFArrayReplaceValues": (
        b"v^{__CFArray=}{CFRange=qq}^@q",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 3, "type_modifier": "n"}}},
    ),
    "CFStringGetCharactersPtr": (
        b"^T^{__CFString=}",
        "",
        {"retval": {"c_array_delimited_by_null": True}},
    ),
    "CFStringTokenizerGoToTokenAtIndex": (b"Q^{__CFStringTokenizer=}q",),
    "CFBundleGetIdentifier": (b"^{__CFString=}^{__CFBundle=}",),
    "CFTreeApplyFunctionToChildren": (
        b"v^{__CFTree=}^?@",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"@"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFDataGetTypeID": (b"Q",),
    "CFRunLoopAddCommonMode": (b"v^{__CFRunLoop=}^{__CFString=}",),
    "CFCalendarSetLocale": (b"v^{__CFCalendar=}^{__CFLocale=}",),
    "CFStreamCreatePairWithSocketToHost": (
        b"v^{__CFAllocator=}^{__CFString=}I^^{__CFReadStream=}^^{__CFWriteStream=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}, 4: {"type_modifier": "o"}},
        },
    ),
    "CFNumberCreate": (
        b"^{__CFNumber=}^{__CFAllocator=}q^v",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateMutable": (
        b"^{__CFString=}^{__CFAllocator=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetAddCharactersInRange": (b"v^{__CFCharacterSet=}{CFRange=qq}",),
    "CFMessagePortIsRemote": (b"Z^{__CFMessagePort=}",),
    "CFURLGetTypeID": (b"Q",),
    "CFReadStreamGetError": (b"{CFStreamError=qi}^{__CFReadStream=}",),
    "CFBagCreateMutableCopy": (
        b"^{__CFBag=}^{__CFAllocator=}q^{__CFBag=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFWriteStreamCreateWithAllocatedBuffers": (
        b"^{__CFWriteStream=}^{__CFAllocator=}^{__CFAllocator=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBagGetTypeID": (b"Q",),
    "CFCalendarGetTimeRangeOfUnit": (
        b"Z^{__CFCalendar=}Qd^d^d",
        "",
        {"arguments": {3: {"type_modifier": "o"}, 4: {"type_modifier": "o"}}},
    ),
    "CFBundlePreflightExecutable": (
        b"Z^{__CFBundle=}^^{__CFError=}",
        "",
        {
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFArrayRemoveAllValues": (b"v^{__CFArray=}",),
    "CFStringGetLineBounds": (
        b"v^{__CFString=}{CFRange=qq}^q^q^q",
        "",
        {
            "arguments": {
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
            }
        },
    ),
    "CFTimeZoneCopyAbbreviation": (
        b"^{__CFString=}^{__CFTimeZone=}d",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFFileSecurityGetGroup": (
        b"Z^{__CFFileSecurity=}^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CFCharacterSetGetPredefined": (b"^{__CFCharacterSet=}q",),
    "CFArrayRemoveValueAtIndex": (b"v^{__CFArray=}q",),
    "CFStringGetSmallestEncoding": (b"I^{__CFString=}",),
    "CFTreeAppendChild": (b"v^{__CFTree=}^{__CFTree=}",),
    "CFURLCreatePropertyFromResource": (
        b"@^{__CFAllocator=}^{__CFURL=}^{__CFString=}^i",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "o"}},
        },
    ),
    "CFURLCopyHostName": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAbsoluteTimeGetDayOfWeek": (b"id^{__CFTimeZone=}",),
    "CFSwapInt64HostToLittle": (b"QQ",),
    "CFArrayExchangeValuesAtIndices": (b"v^{__CFArray=}qq",),
    "CFTimeZoneGetTypeID": (b"Q",),
    "CFRunLoopObserverCreate": (
        b"^{__CFRunLoopObserver=}^{__CFAllocator=}QZq^?^{CFRunLoopObserverContext=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFRunLoopObserver=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFRunLoopTimerGetTolerance": (b"d^{__CFRunLoopTimer=}",),
    "CFBinaryHeapCreateCopy": (
        b"^{__CFBinaryHeap=}^{__CFAllocator=}q^{__CFBinaryHeap=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateFromExternalRepresentation": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFData=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFTimeZoneResetSystem": (b"v",),
    "CFStringNormalize": (b"v^{__CFString=}q",),
    "CFRunLoopContainsSource": (
        b"Z^{__CFRunLoop=}^{__CFRunLoopSource=}^{__CFString=}",
    ),
    "CFLocaleCreateCanonicalLanguageIdentifierFromString": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopGetTypeID": (b"Q",),
    "CFDictionaryApplyFunction": (
        b"v^{__CFDictionary=}^?@",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFLocaleGetLanguageCharacterDirection": (b"q^{__CFString=}",),
    "CFStringCreateArrayWithFindResults": (
        b"^{__CFArray=}^{__CFAllocator=}^{__CFString=}^{__CFString=}{CFRange=qq}Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFArrayGetTypeID": (b"Q",),
    "CFNumberFormatterSetFormat": (b"v^{__CFNumberFormatter=}^{__CFString=}",),
    "CFStreamCreatePairWithSocket": (
        b"v^{__CFAllocator=}i^^{__CFReadStream=}^^{__CFWriteStream=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {2: {"type_modifier": "o"}, 3: {"type_modifier": "o"}},
        },
    ),
    "CFBitVectorCreateMutableCopy": (
        b"^{__CFBitVector=}^{__CFAllocator=}q^{__CFBitVector=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPreferencesGetAppBooleanValue": (
        b"Z^{__CFString=}^{__CFString=}^Z",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFSocketSetAddress": (b"q^{__CFSocket=}^{__CFData=}",),
    "CFRunLoopCopyAllModes": (
        b"^{__CFArray=}^{__CFRunLoop=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateStringByReplacingPercentEscapesUsingEncoding": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFString=}I",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopRun": (b"v",),
    "CFPreferencesGetAppIntegerValue": (
        b"q^{__CFString=}^{__CFString=}^Z",
        "",
        {"arguments": {2: {"type_modifier": "o"}}},
    ),
    "CFMessagePortSetName": (b"Z^{__CFMessagePort=}^{__CFString=}",),
    "CFDateFormatterCreate": (
        b"^{__CFDateFormatter=}^{__CFAllocator=}^{__CFLocale=}qq",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUUIDCreateString": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFUUID=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNumberFormatterCreate": (
        b"^{__CFNumberFormatter=}^{__CFAllocator=}^{__CFLocale=}q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLParserGetStatusCode": (b"q^{__CFXMLParser=}",),
    "CFCalendarGetMinimumRangeOfUnit": (b"{CFRange=qq}^{__CFCalendar=}Q",),
    "CFBitVectorCreateCopy": (
        b"^{__CFBitVector=}^{__CFAllocator=}^{__CFBitVector=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFTimeZoneGetSecondsFromGMT": (b"d^{__CFTimeZone=}d",),
    "CFRunLoopTimerDoesRepeat": (b"Z^{__CFRunLoopTimer=}",),
    "CFCharacterSetCreateInvertedSet": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}^{__CFCharacterSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetParagraphBounds": (
        b"v^{__CFString=}{CFRange=qq}^q^q^q",
        "",
        {
            "arguments": {
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                4: {"type_modifier": "o"},
            }
        },
    ),
    "CFStringGetSystemEncoding": (b"I",),
    "CFBundleCopyResourceURLsOfTypeInDirectory": (
        b"^{__CFArray=}^{__CFURL=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringCreateMutableCopy": (
        b"^{__CFAttributedString=}^{__CFAllocator=}q^{__CFAttributedString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringTokenizerGetCurrentSubTokens": (
        b"q^{__CFStringTokenizer=}^{CFRange=qq}q^{__CFArray=}",
        "",
        {
            "arguments": {
                1: {
                    "c_array_length_in_result": True,
                    "c_array_length_in_arg": 2,
                    "type_modifier": "o",
                }
            }
        },
    ),
    "CFBundleCopyBundleURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLNodeGetInfoPtr": (b"^v^{__CFXMLNode=}",),
    "CFSocketCreateConnectedToSocketSignature": (
        b"^{__CFSocket=}^{__CFAllocator=}^{CFSocketSignature=iii^{__CFData=}}Q^?^{CFSocketContext=q^v^?^?^?}d",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFSocket=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^{__CFData=}"},
                            3: {"type": b"^v"},
                            4: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFURLCreateDataAndPropertiesFromResource": (
        b"Z^{__CFAllocator=}^{__CFURL=}^^{__CFData=}^^{__CFDictionary=}^{__CFArray=}^i",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {"type_modifier": "o"},
                3: {"type_modifier": "o"},
                5: {"type_modifier": "o"},
            },
        },
    ),
    "CFAbsoluteTimeGetWeekOfYear": (b"id^{__CFTimeZone=}",),
    "CFDateFormatterSetProperty": (b"v^{__CFDateFormatter=}^{__CFString=}@",),
    "CFTreeGetTypeID": (b"Q",),
    "CFRunLoopStop": (b"v^{__CFRunLoop=}",),
    "CFNotificationCenterPostNotification": (
        b"v^{__CFNotificationCenter=}^{__CFString=}@^{__CFDictionary=}Z",
    ),
    "CFXMLTreeCreateFromData": (
        b"^{__CFTree=}^{__CFAllocator=}^{__CFData=}^{__CFURL=}Qq",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleCopyBuiltInPlugInsURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLTreeCreateWithDataFromURL": (
        b"^{__CFTree=}^{__CFAllocator=}^{__CFURL=}Qq",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFLocaleCreateComponentsFromLocaleIdentifier": (
        b"^{__CFDictionary=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPropertyListIsValid": (b"Z@q",),
    "CFNumberFormatterGetDecimalInfoForCurrencyCode": (
        b"Z^{__CFString=}^i^d",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CFSocketEnableCallBacks": (b"v^{__CFSocket=}Q",),
    "CFSetCreateCopy": (
        b"^{__CFSet=}^{__CFAllocator=}^{__CFSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFSwapInt64BigToHost": (b"QQ",),
    "CFReadStreamGetTypeID": (b"Q",),
    "CFFileDescriptorCreate": (
        b"^{__CFFileDescriptor=}^{__CFAllocator=}iZ^?^{CFFileDescriptorContext=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFFileDescriptor=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFBagCreateMutable": (
        b"^{__CFBag=}^{__CFAllocator=}q^{CFBagCallBacks=q^?^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateWithString": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDictionaryAddValue": (b"v^{__CFDictionary=}@@",),
    "CFFileSecurityCreateCopy": (
        b"^{__CFFileSecurity=}^{__CFAllocator=}^{__CFFileSecurity=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetRemoveCharactersInString": (b"v^{__CFCharacterSet=}^{__CFString=}",),
    "CFRunLoopRemoveObserver": (
        b"v^{__CFRunLoop=}^{__CFRunLoopObserver=}^{__CFString=}",
    ),
    "CFAttributedStringGetMutableString": (b"^{__CFString=}^{__CFAttributedString=}",),
    "CFNumberFormatterSetProperty": (b"v^{__CFNumberFormatter=}^{__CFString=}@",),
    "CFDictionaryCreate": (
        b"^{__CFDictionary=}^{__CFAllocator=}^^v^^vq^{CFDictionaryKeyCallBacks=q^?^?^?^?^?}^{CFDictionaryValueCallBacks=q^?^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFNumberGetByteSize": (b"q^{__CFNumber=}",),
    "CFXMLParserCopyErrorDescription": (
        b"^{__CFString=}^{__CFXMLParser=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetCreateWithBitmapRepresentation": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleGetValueForInfoDictionaryKey": (b"@^{__CFBundle=}^{__CFString=}",),
    "CFWriteStreamUnscheduleFromRunLoop": (
        b"v^{__CFWriteStream=}^{__CFRunLoop=}^{__CFString=}",
    ),
    "CFAttributedStringCreateCopy": (
        b"^{__CFAttributedString=}^{__CFAllocator=}^{__CFAttributedString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorSetBitAtIndex": (b"v^{__CFBitVector=}qI",),
    "CFReadStreamSetDispatchQueue": (b"v^{__CFReadStream=}@",),
    "CFMessagePortSetDispatchQueue": (b"v^{__CFMessagePort=}@",),
    "CFStringGetNameOfEncoding": (b"^{__CFString=}I",),
    "CFBitVectorSetAllBits": (b"v^{__CFBitVector=}I",),
    "CFSocketGetContext": (b"v^{__CFSocket=}^{CFSocketContext=q^v^?^?^?}",),
    "CFLocaleGetWindowsLocaleCodeFromLocaleIdentifier": (b"I^{__CFString=}",),
    "CFXMLParserGetLineNumber": (b"q^{__CFXMLParser=}",),
    "CFTimeZoneGetDaylightSavingTimeOffset": (b"d^{__CFTimeZone=}d",),
    "CFPreferencesAddSuitePreferencesToApp": (b"v^{__CFString=}^{__CFString=}",),
    "CFURLGetFileSystemRepresentation": (
        b"Z^{__CFURL=}Z^tq",
        "",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type_modifier": "o",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    ),
    "CFSetApplyFunction": (
        b"v^{__CFSet=}^?@",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"@"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFStringCapitalize": (b"v^{__CFString=}^{__CFLocale=}",),
    "CFBinaryHeapGetMinimumIfPresent": (
        b"Z^{__CFBinaryHeap=}^@",
        "",
        {"arguments": {1: {"type_modifier": "o"}}},
    ),
    "CFURLCopyPathExtension": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFLocaleCopyISOCountryCodes": (
        b"^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFLocaleCreateCopy": (
        b"^{__CFLocale=}^{__CFAllocator=}^{__CFLocale=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLEnumeratorSkipDescendents": (b"v^{__CFURLEnumerator=}",),
    "CFBinaryHeapAddValue": (b"v^{__CFBinaryHeap=}@",),
    "CFBinaryHeapGetValues": (b"v^{__CFBinaryHeap=}^^v",),
    "CFDateFormatterGetAbsoluteTimeFromString": (
        b"Z^{__CFDateFormatter=}^{__CFString=}^{CFRange=qq}^d",
        "",
        {"arguments": {2: {"type_modifier": "N"}, 3: {"type_modifier": "o"}}},
    ),
    "CFBundleIsArchitectureLoadable": (b"Zi",),
    "CFTreeSortChildren": (
        b"v^{__CFTree=}^?@",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFURLCopyResourcePropertiesForKeys": (
        b"^{__CFDictionary=}^{__CFURL=}^{__CFArray=}^^{__CFError=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                2: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            },
        },
    ),
    "CFNumberCompare": (b"q^{__CFNumber=}^{__CFNumber=}^v",),
    "CFURLHasDirectoryPath": (b"Z^{__CFURL=}",),
    "CFSwapInt16HostToBig": (b"SS",),
    "CFXMLCreateStringByEscapingEntities": (
        b"^{__CFString=}^{__CFAllocator=}^{__CFString=}^{__CFDictionary=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPreferencesSetMultiple": (
        b"v^{__CFDictionary=}^{__CFArray=}^{__CFString=}^{__CFString=}^{__CFString=}",
    ),
    "CFBagGetValue": (b"@^{__CFBag=}@",),
    "CFBundleGetBundleWithIdentifier": (b"^{__CFBundle=}^{__CFString=}",),
    "CFMakeCollectable": (b"@@",),
    "CFSetGetTypeID": (b"Q",),
    "CFStringAppendFormat": (
        b"v^{__CFString=}^{__CFDictionary=}^{__CFString=}",
        "",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    ),
    "CFNumberGetValue": (b"Z^{__CFNumber=}q^v",),
    "CFStringTokenizerSetString": (
        b"v^{__CFStringTokenizer=}^{__CFString=}{CFRange=qq}",
    ),
    "CFRunLoopGetMain": (b"^{__CFRunLoop=}",),
    "CFDictionaryRemoveAllValues": (b"v^{__CFDictionary=}",),
    "CFPropertyListCreateDeepCopy": (
        b"@^{__CFAllocator=}@Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFUUIDGetTypeID": (b"Q",),
    "CFNotificationCenterPostNotificationWithOptions": (
        b"v^{__CFNotificationCenter=}^{__CFString=}@^{__CFDictionary=}Q",
    ),
    "CFStringLowercase": (b"v^{__CFString=}^{__CFLocale=}",),
    "CFCalendarSetMinimumDaysInFirstWeek": (b"v^{__CFCalendar=}q",),
    "CFRetain": (b"@@",),
    "CFStringGetCharacters": (
        b"v^{__CFString=}{CFRange=qq}^T",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "o"}}},
    ),
    "CFTimeZoneGetName": (b"^{__CFString=}^{__CFTimeZone=}",),
    "CFURLCopyStrictPath": (
        b"^{__CFString=}^{__CFURL=}^Z",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"type_modifier": "o"}},
        },
    ),
    "CFBundleIsExecutableLoaded": (b"Z^{__CFBundle=}",),
    "CFArrayAppendArray": (b"v^{__CFArray=}^{__CFArray=}{CFRange=qq}",),
    "CFNumberFormatterGetTypeID": (b"Q",),
    "CFDateGetTypeID": (b"Q",),
    "CFPreferencesCopyMultiple": (
        b"^{__CFDictionary=}^{__CFArray=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetTypeID": (b"Q",),
    "CFBinaryHeapGetTypeID": (b"Q",),
    "CFTimeZoneCopyLocalizedName": (
        b"^{__CFString=}^{__CFTimeZone=}q^{__CFLocale=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCalendarCopyLocale": (
        b"^{__CFLocale=}^{__CFCalendar=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFFileDescriptorDisableCallBacks": (b"v^{__CFFileDescriptor=}Q",),
    "CFBundleLoadExecutableAndReturnError": (
        b"Z^{__CFBundle=}^^{__CFError=}",
        "",
        {
            "arguments": {
                1: {
                    "already_cfretained": True,
                    "type_modifier": "o",
                    "null_accepted": True,
                }
            }
        },
    ),
    "CFNumberFormatterCreateNumberFromString": (
        b"^{__CFNumber=}^{__CFAllocator=}^{__CFNumberFormatter=}^{__CFString=}^{CFRange=qq}Q",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "N"}},
        },
    ),
    "CFAttributedStringGetAttribute": (
        b"@^{__CFAttributedString=}q^{__CFString=}^{CFRange=qq}",
        "",
        {"arguments": {3: {"type_modifier": "o"}}},
    ),
    "CFURLCopyLastPathComponent": (
        b"^{__CFString=}^{__CFURL=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleCopyResourcesDirectoryURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopGetCurrent": (b"^{__CFRunLoop=}",),
    "CFDateFormatterCreateDateFromString": (
        b"^{__CFDate=}^{__CFAllocator=}^{__CFDateFormatter=}^{__CFString=}^{CFRange=qq}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {3: {"type_modifier": "N"}},
        },
    ),
    "CFURLEnumeratorGetDescendentLevel": (b"q^{__CFURLEnumerator=}",),
    "CFStringGetSurrogatePairForLongCharacter": (
        b"ZI^T",
        "",
        {"arguments": {1: {"c_array_of_fixed_length": 2, "type_modifier": "o"}}},
    ),
    "CFBagApplyFunction": (
        b"v^{__CFBag=}^?@",
        "",
        {
            "arguments": {
                1: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}, 1: {"type": b"@"}},
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFBundleCopySharedSupportURL": (
        b"^{__CFURL=}^{__CFBundle=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFCharacterSetCreateWithCharactersInString": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorGetTypeID": (b"Q",),
    "CFPreferencesCopyKeyList": (
        b"^{__CFArray=}^{__CFString=}^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDateFormatterGetTypeID": (b"Q",),
    "CFRunLoopSourceGetContext": (
        b"v^{__CFRunLoopSource=}^{CFRunLoopSourceContext=q^v^?^?^?^?^?^?^?^?}",
    ),
    "CFBundleGetAllBundles": (b"^{__CFArray=}",),
    "CFFileSecuritySetGroupUUID": (b"Z^{__CFFileSecurity=}^{__CFUUID=}",),
    "CFCharacterSetCreateMutableCopy": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}^{__CFCharacterSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringGetRangeOfComposedCharactersAtIndex": (b"{CFRange=qq}^{__CFString=}q",),
    "CFAttributedStringBeginEditing": (b"v^{__CFAttributedString=}",),
    "CFNumberFormatterGetFormat": (b"^{__CFString=}^{__CFNumberFormatter=}",),
    "CFErrorGetTypeID": (b"Q",),
    "CFURLCopyParameterString": (
        b"^{__CFString=}^{__CFURL=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFAttributedStringGetAttributesAndLongestEffectiveRange": (
        b"^{__CFDictionary=}^{__CFAttributedString=}q{CFRange=qq}^{CFRange=qq}",
        "",
        {"arguments": {3: {"type_modifier": "o"}}},
    ),
    "CFCopyDescription": (
        b"^{__CFString=}@",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFDataDeleteBytes": (b"v^{__CFData=}{CFRange=qq}",),
    "CFWriteStreamGetError": (b"{CFStreamError=qi}^{__CFWriteStream=}",),
    "CFURLCreateResourcePropertiesForKeysFromBookmarkData": (
        b"^{__CFDictionary=}^{__CFAllocator=}^{__CFArray=}^{__CFData=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBitVectorGetFirstIndexOfBit": (b"q^{__CFBitVector=}{CFRange=qq}I",),
    "CFCharacterSetCreateCopy": (
        b"^{__CFCharacterSet=}^{__CFAllocator=}^{__CFCharacterSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFStringCreateMutableWithExternalCharactersNoCopy": (
        b"^{__CFString=}^{__CFAllocator=}^Tqq^{__CFAllocator=}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {1: {"c_array_length_in_arg": 2, "type_modifier": "n"}},
        },
    ),
    "CFRunLoopCopyCurrentMode": (
        b"^{__CFString=}^{__CFRunLoop=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFBundleGetPackageInfo": (
        b"v^{__CFBundle=}^I^I",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "CFCalendarSetFirstWeekday": (b"v^{__CFCalendar=}q",),
    "CFStringGetFastestEncoding": (b"I^{__CFString=}",),
    "CFSocketIsValid": (b"Z^{__CFSocket=}",),
    "CFTreeGetChildren": (
        b"v^{__CFTree=}^^{__CFTree=}",
        "",
        {"arguments": {1: {"c_array_of_variable_length": True, "type_modifier": "o"}}},
    ),
    "CFBundleGetLocalInfoDictionary": (b"^{__CFDictionary=}^{__CFBundle=}",),
    "CFArrayBSearchValues": (
        b"q^{__CFArray=}{CFRange=qq}@^?@",
        "",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                }
            }
        },
    ),
    "CFTreeGetNextSibling": (b"^{__CFTree=}^{__CFTree=}",),
    "CFMessagePortGetTypeID": (b"Q",),
    "CFBagGetCount": (b"q^{__CFBag=}",),
    "CFBagRemoveAllValues": (b"v^{__CFBag=}",),
    "CFCharacterSetCreateBitmapRepresentation": (
        b"^{__CFData=}^{__CFAllocator=}^{__CFCharacterSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLParserGetDocument": (b"^v^{__CFXMLParser=}",),
    "CFXMLNodeGetTypeCode": (b"q^{__CFXMLNode=}",),
    "CFArrayGetValues": (
        b"v^{__CFArray=}{CFRange=qq}^@",
        "",
        {"arguments": {2: {"c_array_length_in_arg": 1, "type_modifier": "o"}}},
    ),
    "CFCharacterSetIsSupersetOfSet": (b"Z^{__CFCharacterSet=}^{__CFCharacterSet=}",),
    "CFRunLoopObserverGetTypeID": (b"Q",),
    "CFAbsoluteTimeGetGregorianDate": (b"{CFGregorianDate=iccccd}d^{__CFTimeZone=}",),
    "CFNotificationCenterRemoveObserver": (
        b"v^{__CFNotificationCenter=}@^{__CFString=}@",
    ),
    "CFCalendarSetTimeZone": (b"v^{__CFCalendar=}^{__CFTimeZone=}",),
    "CFSetCreateMutableCopy": (
        b"^{__CFSet=}^{__CFAllocator=}q^{__CFSet=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLTreeCreateWithNode": (
        b"^{__CFTree=}^{__CFAllocator=}^{__CFXMLNode=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFRunLoopTimerGetOrder": (b"q^{__CFRunLoopTimer=}",),
    "CFTreeCreate": (
        b"^{__CFTree=}^{__CFAllocator=}^{CFTreeContext=q^v^?^?^?}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFURLCreateCopyAppendingPathExtension": (
        b"^{__CFURL=}^{__CFAllocator=}^{__CFURL=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFPreferencesCopyAppValue": (
        b"@^{__CFString=}^{__CFString=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "CFXMLNodeGetString": (b"^{__CFString=}^{__CFXMLNode=}",),
    "CFSocketCreateWithSocketSignature": (
        b"^{__CFSocket=}^{__CFAllocator=}^{CFSocketSignature=iii^{__CFData=}}Q^?^{CFSocketContext=q^v^?^?^?}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^{__CFSocket=}"},
                            1: {"type": b"Q"},
                            2: {"type": b"^{__CFData=}"},
                            3: {"type": b"^v"},
                            4: {"type": b"^v"},
                        },
                    }
                }
            },
        },
    ),
    "CFURLEnumeratorCreateForDirectoryURL": (
        b"^{__CFURLEnumerator=}^{__CFAllocator=}^{__CFURL=}Q^{__CFArray=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
}
aliases = {
    "CGFLOAT_EPSILON": "DBL_EPSILON",
    "CFDateFormatterKey": "CFStringRef",
    "CFNumberFormatterKey": "CFStringRef",
    "CGFLOAT_MIN": "DBL_MIN",
    "CF_TYPED_EXTENSIBLE_ENUM": "_CF_TYPED_EXTENSIBLE_ENUM",
    "CF_EXTENSIBLE_STRING_ENUM": "_CF_TYPED_EXTENSIBLE_ENUM",
    "CF_STRING_ENUM": "_CF_TYPED_ENUM",
    "CFErrorDomain": "CFStringRef",
    "CF_TYPED_ENUM": "_CF_TYPED_ENUM",
    "CFRunLoopMode": "CFStringRef",
    "kCFBookmarkResolutionWithoutMountingMask": "kCFURLBookmarkResolutionWithoutMountingMask",
    "kCFBookmarkResolutionWithoutUIMask": "kCFURLBookmarkResolutionWithoutUIMask",
    "CFCalendarIdentifier": "CFStringRef",
    "CGFLOAT_TYPE": "double",
    "kCFFileSecurityRemoveACL": "_FILESEC_REMOVE_ACL",
    "CFXMLTreeRef": "CFTreeRef",
    "CFStreamPropertyKey": "CFStringRef",
    "CGFLOAT_MAX": "DBL_MAX",
    "CFLocaleKey": "CFStringRef",
    "CFNotificationName": "CFStringRef",
    "CFLocaleIdentifier": "CFStringRef",
}
cftypes = [
    ("CFAllocatorRef", b"^{__CFAllocator=}", "CFAllocatorGetTypeID", None),
    ("CFArrayRef", b"^{__CFArray=}", "CFArrayGetTypeID", "NSArray"),
    (
        "CFAttributedStringRef",
        b"^{__CFAttributedString=}",
        "CFAttributedStringGetTypeID",
        "__NSCFAttributedString,NSCFAttributedString",
    ),
    ("CFBagRef", b"^{__CFBag=}", "CFBagGetTypeID", None),
    ("CFBinaryHeapRef", b"^{__CFBinaryHeap=}", "CFBinaryHeapGetTypeID", None),
    ("CFBitVectorRef", b"^{__CFBitVector=}", "CFBitVectorGetTypeID", None),
    (
        "CFBooleanRef",
        b"^{__CFBoolean=}",
        "CFBooleanGetTypeID",
        "__NSCFBoolean,NSCFBoolean",
    ),
    ("CFBundleRef", b"^{__CFBundle=}", "CFBundleGetTypeID", None),
    (
        "CFCalendarRef",
        b"^{__CFCalendar=}",
        "CFCalendarGetTypeID",
        "__NSCFCalendar,NSCFCalendar",
    ),
    (
        "CFCharacterSetRef",
        b"^{__CFCharacterSet=}",
        "CFCharacterSetGetTypeID",
        "__NSCFCharacterSet,NSCFCharacterSet",
    ),
    ("CFDataRef", b"^{__CFData=}", "CFDataGetTypeID", "__NSCFData,NSCFData"),
    ("CFDateFormatterRef", b"^{__CFDateFormatter=}", "CFDateFormatterGetTypeID", None),
    ("CFDateRef", b"^{__CFDate=}", "CFDateGetTypeID", "__NSCFDate,NSCFDate,__NSDate"),
    ("CFDictionaryRef", b"^{__CFDictionary=}", "CFDictionaryGetTypeID", "NSDictionary"),
    ("CFErrorRef", b"^{__CFError=}", "CFErrorGetTypeID", "__NSCFError,NSCFError"),
    (
        "CFFileDescriptorRef",
        b"^{__CFFileDescriptor=}",
        "CFFileDescriptorGetTypeID",
        None,
    ),
    (
        "CFFileSecurityRef",
        b"^{__CFFileSecurity=}",
        "CFFileSecurityGetTypeID",
        "__NSFileSecurity",
    ),
    ("CFLocaleRef", b"^{__CFLocale=}", "CFLocaleGetTypeID", "__NSCFLocale,NSCFLocale"),
    ("CFMachPortRef", b"^{__CFMachPort=}", "CFMachPortGetTypeID", "NSMachPort"),
    ("CFMessagePortRef", b"^{__CFMessagePort=}", "CFMessagePortGetTypeID", None),
    ("CFMutableArrayRef", b"^{__CFArray=}", "CFArrayGetTypeID", "NSMutableArray"),
    (
        "CFMutableAttributedStringRef",
        b"^{__CFAttributedString=}",
        "CFAttributedStringGetTypeID",
        "__NSCFAttributedString,NSCFAttributedString",
    ),
    ("CFMutableBagRef", b"^{__CFBag=}", "CFBagGetTypeID", None),
    ("CFMutableBitVectorRef", b"^{__CFBitVector=}", "CFBitVectorGetTypeID", None),
    (
        "CFMutableCharacterSetRef",
        b"^{__CFCharacterSet=}",
        "CFCharacterSetGetTypeID",
        None,
    ),
    ("CFMutableDataRef", b"^{__CFData=}", "CFDataGetTypeID", "NSMutableData"),
    (
        "CFMutableDictionaryRef",
        b"^{__CFDictionary=}",
        "CFDictionaryGetTypeID",
        "NSMutableDictionary",
    ),
    ("CFMutableSetRef", b"^{__CFSet=}", "CFSetGetTypeID", "NSMutableSet"),
    ("CFMutableStringRef", b"@", "CFStringGetTypeID", "NSMutableString"),
    (
        "CFNotificationCenterRef",
        b"^{__CFNotificationCenter=}",
        "CFNotificationCenterGetTypeID",
        None,
    ),
    ("CFNullRef", b"^{__CFNull=}", "CFNullGetTypeID", "NSNull"),
    (
        "CFNumberFormatterRef",
        b"^{__CFNumberFormatter=}",
        "CFNumberFormatterGetTypeID",
        None,
    ),
    ("CFNumberRef", b"^{__CFNumber=}", "CFNumberGetTypeID", "__NSCFNumber,NSCFNumber"),
    (
        "CFPlugInInstanceRef",
        b"^{__CFPlugInInstance=}",
        "CFPlugInInstanceGetTypeID",
        None,
    ),
    (
        "CFReadStreamRef",
        b"^{__CFReadStream=}",
        "CFReadStreamGetTypeID",
        "__NSCFInputStream,NSCFInputStream",
    ),
    (
        "CFRunLoopObserverRef",
        b"^{__CFRunLoopObserver=}",
        "CFRunLoopObserverGetTypeID",
        None,
    ),
    ("CFRunLoopRef", b"^{__CFRunLoop=}", "CFRunLoopGetTypeID", None),
    ("CFRunLoopSourceRef", b"^{__CFRunLoopSource=}", "CFRunLoopSourceGetTypeID", None),
    (
        "CFRunLoopTimerRef",
        b"^{__CFRunLoopTimer=}",
        "CFRunLoopTimerGetTypeID",
        "__NSCFTimer,NSCFTimer",
    ),
    ("CFSetRef", b"^{__CFSet=}", "CFSetGetTypeID", "NSSet"),
    ("CFSocketRef", b"^{__CFSocket=}", "CFSocketGetTypeID", None),
    ("CFStringRef", b"^{__CFString=}", "CFStringGetTypeID", "NSString"),
    (
        "CFStringTokenizerRef",
        b"^{__CFStringTokenizer=}",
        "CFStringTokenizerGetTypeID",
        None,
    ),
    ("CFTimeZoneRef", b"^{__CFTimeZone=}", "CFTimeZoneGetTypeID", "NSTimeZone"),
    ("CFTreeRef", b"^{__CFTree=}", "CFTreeGetTypeID", None),
    ("CFURLEnumeratorRef", b"^{__CFURLEnumerator=}", "CFURLEnumeratorGetTypeID", None),
    ("CFURLRef", b"^{__CFURL=}", "CFURLGetTypeID", "NSURL"),
    ("CFUUIDRef", b"^{__CFUUID=}", "CFUUIDGetTypeID", None),
    (
        "CFUserNotificationRef",
        b"^{__CFUserNotification=}",
        "CFUserNotificationGetTypeID",
        None,
    ),
    (
        "CFWriteStreamRef",
        b"^{__CFWriteStream=}",
        "CFWriteStreamGetTypeID",
        "__NSCFOutputStream,NSCFOutputStream",
    ),
    ("CFXMLNodeRef", b"^{__CFXMLNode=}", "CFXMLNodeGetTypeID", None),
    ("CFXMLParserRef", b"^{__CFXMLParser=}", "CFXMLParserGetTypeID", None),
]
expressions = {
    "kCFISO8601DateFormatWithFullTime": "kCFISO8601DateFormatWithTime | kCFISO8601DateFormatWithColonSeparatorInTime | kCFISO8601DateFormatWithTimeZone | kCFISO8601DateFormatWithColonSeparatorInTimeZone",
    "kCFISO8601DateFormatWithFullDate": "kCFISO8601DateFormatWithYear | kCFISO8601DateFormatWithMonth | kCFISO8601DateFormatWithDay | kCFISO8601DateFormatWithDashSeparatorInDate",
    "kCFISO8601DateFormatWithInternetDateTime": "kCFISO8601DateFormatWithFullDate | kCFISO8601DateFormatWithFullTime",
}

# END OF FILE
