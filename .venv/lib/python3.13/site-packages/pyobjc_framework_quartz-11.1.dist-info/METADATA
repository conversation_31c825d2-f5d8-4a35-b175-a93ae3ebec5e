Metadata-Version: 2.4
Name: pyobjc-framework-Quartz
Version: 11.1
Summary: Wrappers for the Quartz frameworks on macOS
Home-page: https://github.com/ronal<PERSON>ussoren/pyobjc
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: PyObjC,Quartz,Quartz.CoreGraphics,Quartz.CoreVideo,Quartz.ImageIO,Quartz.ImageKit,Quartz.PDFKit,Quartz.QuartzComposer,Quartz.QuartzCore,Quartz.QuartzFilters,Quartz.QuickLookUI
Platform: MacOS X
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X :: Cocoa
Classifier: Intended Audience :: Developers
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Objective C
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Software Development :: User Interfaces
Requires-Python: >=3.9
Description-Content-Type: text/x-rst; charset=UTF-8
License-File: LICENSE.txt
Requires-Dist: pyobjc-core>=11.1
Requires-Dist: pyobjc-framework-Cocoa>=11.1
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary
Project-URL: Documentation, https://pyobjc.readthedocs.io/en/latest/
Project-URL: Issue tracker, https://github.com/ronaldoussoren/pyobjc/issues
Project-URL: Repository, https://github.com/ronaldoussoren/pyobjc


Wrappers for the "Quartz" related frameworks on macOS. These frameworks
provide a number of graphics related API's.

The frameworks wrapped by this package are:

   * CoreGraphics - 2D Graphics, based on the PDF model

   * ImageIO - Reading and writing images

   * QuartzComposer - Working with "Quartz Composer" compositions

   * QuartzCore  - Image processing and video image manipulation

   * QuarzFilters - Image effects

   * ImageKit - iPhoto-like views

   * PDFKit - Working with PDF files

   * CoreVideo - Managing digital video

All frameworks can be accessed by importing the 'Quartz' module.

These wrappers don't include documentation, please check Apple's documentation
for information on how to use this framework and PyObjC's documentation
for general tips and tricks regarding the translation between Python
and (Objective-)C frameworks

NOTE: The actual wrappers are subpackages of ``Quartz``, they are not toplevel
packages to avoid name clashes with Apple provided wrappers for CoreGraphics.

WARNING: Running the unittests will change your display settings during the
testrun, which will probably mess up your window layout.

NEWS
====

2.4
---

* Add wrapper for ``CGBitmapContextCreateWithData``



Project links
-------------

* `Documentation <https://pyobjc.readthedocs.io/en/latest/>`_

* `Issue Tracker <https://github.com/ronaldoussoren/pyobjc/issues>`_

* `Repository <https://github.com/ronaldoussoren/pyobjc/>`_

